# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
*.tmp

# Git
.git/
.gitignore

# Documentation build
docs/_build/

# Test files
.pytest_cache/
.coverage
htmlcov/

# Large video files (optional - comment out if you want to include them)
*.avi
*.mp4
*.mov
*.mkv
*.wmv
*.flv

# Logs
*.log

# Docker files (avoid recursion)
Dockerfile*
docker-compose*.yml
.dockerignore
