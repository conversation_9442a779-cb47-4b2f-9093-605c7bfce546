# Docker Deployment Guide

This guide explains how to run the Wagons Recognition application using Docker for easy deployment and consistent environments.

## Prerequisites

- Docker Engine 20.10+ 
- Docker Compose 2.0+
- X11 server (for GUI display)

### Platform-Specific Setup

#### Linux
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo apt-get install docker-compose-plugin

# Allow X11 forwarding
xhost +local:docker
```

#### Windows
```bash
# Install Docker Desktop
# Download from: https://www.docker.com/products/docker-desktop

# Install VcXsrv for X11 forwarding
# Download from: https://sourceforge.net/projects/vcxsrv/
# Run XLaunch with "Disable access control" checked
```

#### macOS
```bash
# Install Docker Desktop
# Download from: https://www.docker.com/products/docker-desktop

# Install XQuartz for X11 forwarding
brew install --cask xquartz
# Start XQuartz and enable "Allow connections from network clients"
```

## Quick Start

### 1. Build and Run with Docker Compose (Recommended)

```bash
# Clone/navigate to the project directory
cd wagons_recognition_python

# Build and start the application
docker compose up --build

# Run in detached mode
docker compose up -d --build

# View logs
docker compose logs -f

# Stop the application
docker compose down
```

### 2. Build and Run with Docker Commands

```bash
# Build the image
docker build -t wagons-recognition .

# Run with GUI support (Linux)
docker run -it --rm \
  -e DISPLAY=$DISPLAY \
  -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
  -v $(pwd)/videos:/app/videos:rw \
  --device /dev/video0:/dev/video0 \
  wagons-recognition

# Run with GUI support (Windows with VcXsrv)
docker run -it --rm \
  -e DISPLAY=host.docker.internal:0 \
  -v %cd%/videos:/app/videos:rw \
  wagons-recognition

# Run with GUI support (macOS with XQuartz)
docker run -it --rm \
  -e DISPLAY=host.docker.internal:0 \
  -v $(pwd)/videos:/app/videos:rw \
  wagons-recognition
```

## Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DISPLAY` | `:0` | X11 display for GUI |
| `PYTHONPATH` | `/app` | Python module path |

### Volume Mounts

| Host Path | Container Path | Purpose |
|-----------|----------------|---------|
| `./videos` | `/app/videos` | Video files directory |
| `/tmp/.X11-unix` | `/tmp/.X11-unix` | X11 socket (Linux) |

### Device Access

| Device | Purpose |
|--------|---------|
| `/dev/video0` | Default camera access |

## Usage Examples

### 1. Process Video Files

```bash
# Place video files in the videos/ directory
cp your_wagon_video.mp4 videos/

# Run the application
docker-compose up

# Select "Video file" option and choose your video
```

### 2. Use Camera Input

```bash
# Ensure camera is connected and accessible
ls /dev/video*

# Run with camera access
docker-compose up

# Select "Camera" option in the application
```

### 3. Headless Mode (Future Feature)

```bash
# Run without GUI (for batch processing)
docker run -it --rm \
  -v $(pwd)/videos:/app/videos:rw \
  wagons-recognition \
  python batch_process.py --input /app/videos --output /app/results
```

## Troubleshooting

### GUI Issues

#### Linux
```bash
# Allow X11 connections
xhost +local:docker

# Check DISPLAY variable
echo $DISPLAY

# Test X11 forwarding
docker run --rm -e DISPLAY=$DISPLAY -v /tmp/.X11-unix:/tmp/.X11-unix:rw alpine sh -c "apk add --no-cache xeyes && xeyes"
```

#### Windows
```bash
# Ensure VcXsrv is running
# Check "Disable access control" in XLaunch
# Set DISPLAY environment variable
set DISPLAY=host.docker.internal:0
```

#### macOS
```bash
# Start XQuartz
open -a XQuartz

# Allow network connections
# XQuartz > Preferences > Security > "Allow connections from network clients"

# Set DISPLAY
export DISPLAY=host.docker.internal:0
```

### Camera Issues

```bash
# Check camera permissions (Linux)
sudo usermod -a -G video $USER
sudo chmod 666 /dev/video0

# List available cameras
v4l2-ctl --list-devices

# Test camera access
docker run --rm --device /dev/video0:/dev/video0 alpine sh -c "ls -la /dev/video0"
```

### Performance Issues

```bash
# Increase memory limit
docker-compose up --scale wagons-recognition=1 --memory=4g

# Use GPU acceleration (if available)
docker run --gpus all wagons-recognition
```

## Development Mode

### Live Code Editing

```bash
# Mount source code for development
docker run -it --rm \
  -e DISPLAY=$DISPLAY \
  -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
  -v $(pwd):/app:rw \
  wagons-recognition \
  bash

# Inside container, run the application
python main.py
```

### Debug Mode

```bash
# Run with debug logging
docker run -it --rm \
  -e DISPLAY=$DISPLAY \
  -e PYTHONPATH=/app \
  -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
  -v $(pwd)/videos:/app/videos:rw \
  wagons-recognition \
  python -u main.py --debug
```

## Image Information

- **Base Image**: python:3.11-slim
- **Size**: ~800MB (optimized multi-stage build)
- **Architecture**: linux/amd64, linux/arm64
- **Security**: Runs as non-root user
- **Health Check**: Included

## Building for Production

```bash
# Build optimized production image
docker build --target production -t wagons-recognition:prod .

# Build for multiple architectures
docker buildx build --platform linux/amd64,linux/arm64 -t wagons-recognition:latest .

# Push to registry
docker tag wagons-recognition:latest your-registry/wagons-recognition:latest
docker push your-registry/wagons-recognition:latest
```

## Security Considerations

- Application runs as non-root user (UID 1000)
- Minimal attack surface with slim base image
- No unnecessary packages installed
- Camera access requires explicit device mounting
- X11 forwarding should be used carefully in production

## Resource Requirements

- **Minimum**: 512MB RAM, 1 CPU core
- **Recommended**: 2GB RAM, 2 CPU cores
- **Storage**: ~1GB for image + video files
- **GPU**: Optional, for enhanced performance
