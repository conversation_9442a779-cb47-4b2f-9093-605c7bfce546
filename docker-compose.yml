version: '3.8'

services:
  wagons-recognition:
    build:
      context: .
      dockerfile: Dockerfile
    image: wagons-recognition:latest
    container_name: wagons-recognition-app

    # Allow interactive input (stdin) and allocate a TTY
    stdin_open: true  # equivalent to -i
    tty: true         # equivalent to -t

    # Environment variables
    environment:
      - DISPLAY=${DISPLAY:-:0}
      - PYTHONPATH=/app
      # Optional non-interactive configuration (uncomment to use)
      # - INPUT_SOURCE=video      # or 'camera'
      # - VIDEO_PATH=/app/videos/your_video.mp4

    # Volume mounts
    volumes:
      # X11 socket for GUI (Linux/macOS)
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      # Video files directory (bind mount for easy access)
      - ./videos:/app/videos:rw
      # Optional: Mount additional video sources
      # - /path/to/your/videos:/app/videos:ro

    # Device access for camera
    devices:
      - /dev/video0:/dev/video0  # Default camera (Linux)

    # Network mode for GUI
    network_mode: host

    # Security options
    security_opt:
      - seccomp:unconfined

    # Restart policy (set to 'no' for interactive usage)
    restart: "no"

    # Resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Optional: Web interface service (for future development)
  # wagons-web:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.web
  #   ports:
  #     - "8080:8080"
  #   depends_on:
  #     - wagons-recognition
  #   volumes:
  #     - ./videos:/app/videos:ro
