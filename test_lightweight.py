#!/usr/bin/env python3
"""
Lightweight test for the railway wagon recognition system.
Tests components that don't require heavy ML dependencies.
"""

import sys
import logging
from pathlib import Path
import numpy as np
import cv2

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_basic_components():
    """Test basic components that don't require ML libraries."""
    logger.info("Testing Basic Components (No ML Dependencies)")
    logger.info("=" * 50)
    
    success_count = 0
    total_tests = 0
    
    # Test Configuration
    total_tests += 1
    try:
        from app.core.config import settings
        logger.info(f"✅ Configuration: {settings.APP_NAME} v{settings.APP_VERSION}")
        logger.info(f"   Models dir: {settings.MODELS_DIR}")
        logger.info(f"   Target FPS: {settings.TARGET_FPS}")
        success_count += 1
    except Exception as e:
        logger.error(f"❌ Configuration failed: {e}")
    
    # Test Number Validator (no ML dependencies)
    total_tests += 1
    try:
        from ai.validator import NumberValidator
        validator = NumberValidator()
        
        # Test validation
        test_numbers = ["12345678", "1234567", "ABCD1234", "87654321", ""]
        valid_count = 0
        
        for number in test_numbers:
            result = validator.validate_with_details(number)
            if result['is_valid']:
                valid_count += 1
            logger.info(f"   '{number}': {'✓' if result['is_valid'] else '✗'} "
                       f"(confidence: {result['confidence_score']:.2f})")
        
        logger.info(f"✅ Number Validator: {valid_count}/{len(test_numbers)} numbers valid")
        success_count += 1
    except Exception as e:
        logger.error(f"❌ Number Validator failed: {e}")
    
    # Test Legacy Detection (works without ML)
    total_tests += 1
    try:
        from detection import MorphologyDetector
        detector = MorphologyDetector()
        
        # Create test image with some patterns
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Add some rectangular patterns
        cv2.rectangle(test_image, (100, 200), (500, 300), (255, 255, 255), -1)
        cv2.rectangle(test_image, (120, 220), (180, 280), (0, 0, 0), -1)
        cv2.rectangle(test_image, (200, 220), (260, 280), (0, 0, 0), -1)
        
        detector.detect(test_image)
        rects = detector.get_rects()
        
        logger.info(f"✅ Legacy Morphological Detector: {len(rects)} detections")
        success_count += 1
    except Exception as e:
        logger.error(f"❌ Legacy Detector failed: {e}")
    
    # Test Video Manager
    total_tests += 1
    try:
        from utils import VideoManager
        vm = VideoManager()
        
        videos = vm.get_available_videos()
        fps = vm.get_fps()
        
        logger.info(f"✅ Video Manager: {len(videos)} videos found, default FPS: {fps}")
        success_count += 1
    except Exception as e:
        logger.error(f"❌ Video Manager failed: {e}")
    
    # Test Image Utils
    total_tests += 1
    try:
        from utils import convert_to_gray, concat_images, Direction
        
        # Test image conversion
        color_img = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        gray_img = convert_to_gray(color_img)
        
        # Test concatenation
        img1 = np.random.randint(0, 255, (100, 100), dtype=np.uint8)
        img2 = np.random.randint(0, 255, (100, 100), dtype=np.uint8)
        
        concat_h = concat_images(img1, img2, Direction.HORIZONTAL)
        concat_v = concat_images(img1, img2, Direction.VERTICAL)
        
        logger.info(f"✅ Image Utils: Gray conversion {gray_img.shape}, "
                   f"H-concat {concat_h.shape}, V-concat {concat_v.shape}")
        success_count += 1
    except Exception as e:
        logger.error(f"❌ Image Utils failed: {e}")
    
    return success_count, total_tests


def test_web_api_basic():
    """Test basic web API without heavy dependencies."""
    logger.info("\nTesting Web API (Basic)")
    logger.info("=" * 30)
    
    try:
        # Test if we can import FastAPI components
        from app.core.config import settings
        from fastapi import FastAPI
        
        # Create a simple app instance
        app = FastAPI(title=settings.APP_NAME)
        
        logger.info(f"✅ FastAPI app created: {settings.APP_NAME}")
        
        # Test if we can create basic endpoints
        @app.get("/test")
        def test_endpoint():
            return {"status": "ok", "message": "Basic API working"}
        
        logger.info("✅ Basic endpoint created")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Web API basic test failed: {e}")
        return False


def test_improved_detection():
    """Test the improved detection with your video."""
    logger.info("\nTesting Improved Detection on Your Video")
    logger.info("=" * 40)
    
    try:
        from utils import VideoManager
        from detection import MorphologyDetector
        
        # Try to open your video
        vm = VideoManager()
        videos = vm.get_available_videos()
        
        if not videos:
            logger.warning("No videos found - skipping video test")
            return True
        
        # Use the first available video
        video_path = videos[0]
        logger.info(f"Testing with video: {video_path.name}")
        
        if not vm.open_video_file(video_path):
            logger.error(f"Failed to open video: {video_path}")
            return False
        
        # Create enhanced detector
        detector = MorphologyDetector()
        
        # Process a few frames
        frame_count = 0
        detection_count = 0
        
        for i in range(10):  # Test first 10 frames
            success, frame = vm.read_frame()
            if not success:
                break
            
            frame_count += 1
            
            # Detect with original method
            detector.detect(frame)
            rects = detector.get_rects()
            
            if rects:
                detection_count += 1
                logger.info(f"   Frame {i+1}: {len(rects)} detections")
        
        vm.release()
        
        logger.info(f"✅ Video Test: {detection_count}/{frame_count} frames had detections")
        
        if detection_count == 0:
            logger.warning("⚠️  No detections found - this is the issue we need to fix!")
            logger.info("   Suggestions:")
            logger.info("   1. Adjust detection parameters in config.py")
            logger.info("   2. Use the debug_detection.py tool to tune parameters")
            logger.info("   3. Install AI dependencies for better detection")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Video detection test failed: {e}")
        return False


def main():
    """Run lightweight tests."""
    logger.info("Railway Wagon Recognition - Lightweight Test Suite")
    logger.info("=" * 60)
    
    # Test basic components
    basic_passed, basic_total = test_basic_components()
    
    # Test web API
    api_passed = test_web_api_basic()
    
    # Test video detection
    video_passed = test_improved_detection()
    
    # Summary
    total_passed = basic_passed + (1 if api_passed else 0) + (1 if video_passed else 0)
    total_tests = basic_total + 2
    
    logger.info("\n" + "=" * 60)
    logger.info(f"LIGHTWEIGHT TEST RESULTS: {total_passed}/{total_tests} tests passed")
    
    if total_passed >= total_tests - 1:  # Allow one failure
        logger.info("🎉 Core system is working! Ready for AI enhancement.")
        logger.info("\nNext steps to fix the detection issue:")
        logger.info("1. 📊 Run parameter tuning: python debug_detection.py")
        logger.info("2. 🧠 Install AI dependencies (in progress)")
        logger.info("3. 🚀 Start web interface: python app/main.py")
        return 0
    else:
        logger.warning("⚠️  Some core components failed. Check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
