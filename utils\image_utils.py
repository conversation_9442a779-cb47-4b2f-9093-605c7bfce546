"""
Image processing utility functions.
"""

from enum import Enum
from typing import Tuple
import cv2
import numpy as np


class Direction(Enum):
    """Direction for image concatenation."""
    HORIZONTAL = "horizontal"
    VERTICAL = "vertical"


def convert_to_gray(image: np.ndarray) -> np.ndarray:
    """
    Convert image to grayscale if it's not already.
    
    Args:
        image: Input image as numpy array
        
    Returns:
        Grayscale image
    """
    if len(image.shape) == 3:
        return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        return image.copy()


def concat_images(
    first: np.ndarray, 
    second: np.ndarray, 
    direction: Direction = Direction.HORIZONTAL
) -> np.ndarray:
    """
    Concatenate two images in the specified direction.
    
    Args:
        first: First image
        second: Second image  
        direction: Direction for concatenation (horizontal or vertical)
        
    Returns:
        Concatenated image
        
    Raises:
        ValueError: If images cannot be concatenated due to size mismatch
    """
    # Convert both images to grayscale for consistency
    gray1 = convert_to_gray(first)
    gray2 = convert_to_gray(second)
    
    if direction == Direction.HORIZONTAL:
        # Check if heights match
        if gray1.shape[0] != gray2.shape[0]:
            # Resize second image to match first image's height
            new_height = gray1.shape[0]
            new_width = int(gray2.shape[1] * new_height / gray2.shape[0])
            gray2 = cv2.resize(gray2, (new_width, new_height))
        
        # Concatenate horizontally
        result = np.hstack((gray1, gray2))
        
    else:  # VERTICAL
        # Check if widths match
        if gray1.shape[1] != gray2.shape[1]:
            # Resize second image to match first image's width
            new_width = gray1.shape[1]
            new_height = int(gray2.shape[0] * new_width / gray2.shape[1])
            gray2 = cv2.resize(gray2, (new_width, new_height))
        
        # Concatenate vertically
        result = np.vstack((gray1, gray2))
    
    return result


def resize_image(
    image: np.ndarray, 
    target_size: Tuple[int, int], 
    keep_aspect_ratio: bool = True
) -> np.ndarray:
    """
    Resize image to target size.
    
    Args:
        image: Input image
        target_size: Target size as (width, height)
        keep_aspect_ratio: Whether to maintain aspect ratio
        
    Returns:
        Resized image
    """
    if keep_aspect_ratio:
        h, w = image.shape[:2]
        target_w, target_h = target_size
        
        # Calculate scaling factor
        scale = min(target_w / w, target_h / h)
        
        # Calculate new dimensions
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        return cv2.resize(image, (new_w, new_h))
    else:
        return cv2.resize(image, target_size)


def apply_sobel_filter(image: np.ndarray) -> np.ndarray:
    """
    Apply Sobel filter to detect edges.
    
    Args:
        image: Input image
        
    Returns:
        Filtered image with detected edges
    """
    # Convert to grayscale if needed
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()
    
    # Calculate gradients
    grad_x = cv2.Sobel(gray, cv2.CV_16S, 1, 0, ksize=3, scale=1, delta=0)
    grad_y = cv2.Sobel(gray, cv2.CV_16S, 0, 1, ksize=3, scale=1, delta=0)
    
    # Convert to absolute values
    abs_grad_x = cv2.convertScaleAbs(grad_x)
    abs_grad_y = cv2.convertScaleAbs(grad_y)
    
    # Combine gradients
    result = cv2.addWeighted(abs_grad_x, 0.5, abs_grad_y, 0.5, 0)
    
    return result
