"""
OCR Engine for Railway Wagon Number Recognition

Implements EasyOCR-based text recognition for wagon numbers.
Handles various lighting conditions, distortions, and partial damage.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

import cv2
import numpy as np

from app.core.config import settings

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    logging.warning("EasyOCR not available. Install with: pip install easyocr")


class NumberOCR:
    """
    EasyOCR-based text recognition for railway wagon numbers.
    
    Features:
    - Multi-language support (Latin, Cyrillic)
    - Robust to lighting variations and distortions
    - Confidence scoring for each recognition
    - Image preprocessing for better accuracy
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.reader = None
        self.languages = ['en', 'ru']  # English and Russian for railway numbers
        
        if not EASYOCR_AVAILABLE:
            self.logger.warning("EasyOCR not available - using mock OCR")
    
    def initialize(self, gpu: bool = False) -> bool:
        """
        Initialize the OCR engine.
        
        Args:
            gpu: Whether to use GPU acceleration
            
        Returns:
            True if initialization successful, False otherwise
        """
        if not EASYOCR_AVAILABLE:
            self.logger.info("Using mock OCR (EasyOCR not available)")
            return True
        
        try:
            self.logger.info(f"Initializing EasyOCR with languages: {self.languages}")
            self.reader = easyocr.Reader(
                self.languages,
                gpu=gpu,
                verbose=False
            )
            
            # Test with dummy image
            dummy_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
            cv2.putText(dummy_image, "12345678", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            _ = self.reader.readtext(dummy_image)
            
            self.logger.info("EasyOCR initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize EasyOCR: {e}")
            return False
    
    def recognize(self, image: np.ndarray, preprocess: bool = True) -> List[Dict[str, Any]]:
        """
        Recognize text in an image.
        
        Args:
            image: Input image containing text
            preprocess: Whether to apply preprocessing
            
        Returns:
            List of recognition results with text, confidence, and bbox
        """
        if not EASYOCR_AVAILABLE or self.reader is None:
            return self._mock_recognize(image)
        
        try:
            # Preprocess image if requested
            if preprocess:
                processed_image = self._preprocess_image(image)
            else:
                processed_image = image
            
            # Run OCR
            results = self.reader.readtext(processed_image)
            
            # Process results
            recognitions = []
            for result in results:
                bbox, text, confidence = result
                
                # Filter and clean text
                cleaned_text = self._clean_text(text)
                if self._is_valid_number(cleaned_text):
                    recognition = {
                        'text': cleaned_text,
                        'confidence': float(confidence),
                        'bbox': bbox,
                        'raw_text': text
                    }
                    recognitions.append(recognition)
            
            # Sort by confidence
            recognitions.sort(key=lambda x: x['confidence'], reverse=True)
            
            return recognitions
            
        except Exception as e:
            self.logger.error(f"Error during OCR recognition: {e}")
            return []
    
    def recognize_batch(self, images: List[np.ndarray], preprocess: bool = True) -> List[List[Dict[str, Any]]]:
        """
        Recognize text in multiple images.
        
        Args:
            images: List of input images
            preprocess: Whether to apply preprocessing
            
        Returns:
            List of recognition results for each image
        """
        if not images:
            return []
        
        results = []
        for image in images:
            result = self.recognize(image, preprocess)
            results.append(result)
        
        return results
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image for better OCR accuracy.
        
        Args:
            image: Input image
            
        Returns:
            Preprocessed image
        """
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # Resize if too small
        height, width = gray.shape
        if height < 50 or width < 100:
            scale_factor = max(50 / height, 100 / width)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        # Enhance contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # Denoise
        denoised = cv2.fastNlMeansDenoising(enhanced)
        
        # Sharpen
        kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        
        # Threshold for better text contrast
        _, binary = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return binary
    
    def _clean_text(self, text: str) -> str:
        """
        Clean and normalize recognized text.
        
        Args:
            text: Raw OCR text
            
        Returns:
            Cleaned text
        """
        # Remove non-alphanumeric characters
        cleaned = re.sub(r'[^0-9A-Za-z]', '', text)
        
        # Convert to uppercase
        cleaned = cleaned.upper()
        
        # Common OCR corrections for numbers
        corrections = {
            'O': '0', 'I': '1', 'L': '1', 'S': '5', 'G': '6', 'B': '8'
        }
        
        for wrong, correct in corrections.items():
            cleaned = cleaned.replace(wrong, correct)
        
        return cleaned
    
    def _is_valid_number(self, text: str) -> bool:
        """
        Check if text looks like a valid wagon number.
        
        Args:
            text: Text to validate
            
        Returns:
            True if text looks like a wagon number
        """
        if not text:
            return False
        
        # Check length
        if len(text) < settings.MIN_NUMBER_LENGTH or len(text) > settings.MAX_NUMBER_LENGTH:
            return False
        
        # Check if mostly digits
        digit_count = sum(1 for c in text if c.isdigit())
        if digit_count < len(text) * 0.7:  # At least 70% digits
            return False
        
        return True
    
    def _mock_recognize(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        Mock OCR for development/testing when EasyOCR is not available.
        
        Uses simple template matching and heuristics.
        """
        try:
            # Convert to grayscale
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # Simple pattern detection for numbers
            height, width = gray.shape
            
            # Look for text-like regions (high contrast areas)
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Find contours that might be digits
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours by size and aspect ratio
            digit_contours = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h if h > 0 else 0
                area = w * h
                
                # Digit-like proportions
                if 0.3 <= aspect_ratio <= 1.0 and 50 <= area <= 2000:
                    digit_contours.append((x, y, w, h))
            
            if len(digit_contours) >= 4:  # At least 4 digits for a number
                # Sort by x position (left to right)
                digit_contours.sort(key=lambda c: c[0])
                
                # Generate mock number
                mock_number = ''.join([str(i % 10) for i in range(len(digit_contours))])
                
                # Calculate overall bounding box
                min_x = min(c[0] for c in digit_contours)
                min_y = min(c[1] for c in digit_contours)
                max_x = max(c[0] + c[2] for c in digit_contours)
                max_y = max(c[1] + c[3] for c in digit_contours)
                
                bbox = [[min_x, min_y], [max_x, min_y], [max_x, max_y], [min_x, max_y]]
                
                return [{
                    'text': mock_number,
                    'confidence': 0.75,  # Mock confidence
                    'bbox': bbox,
                    'raw_text': mock_number
                }]
            
            return []
            
        except Exception as e:
            self.logger.error(f"Error in mock OCR: {e}")
            return []
    
    def get_engine_info(self) -> Dict[str, Any]:
        """Get information about the OCR engine."""
        if not EASYOCR_AVAILABLE:
            return {
                'status': 'mock',
                'reason': 'EasyOCR not available',
                'languages': self.languages
            }
        
        if self.reader is None:
            return {'status': 'not_initialized'}
        
        return {
            'status': 'initialized',
            'engine': 'EasyOCR',
            'languages': self.languages,
            'gpu_enabled': hasattr(self.reader, 'device') and 'cuda' in str(self.reader.device)
        }
