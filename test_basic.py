#!/usr/bin/env python3
"""
Basic test script to verify the implementation works correctly.
"""

import sys
from pathlib import Path
import numpy as np
import cv2

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all modules can be imported correctly."""
    print("Testing imports...")
    
    try:
        from detection import Detector, MorphologyDetector
        print("✓ Detection modules imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import detection modules: {e}")
        return False
    
    try:
        from utils import VideoManager, convert_to_gray, concat_images, Direction
        print("✓ Utility modules imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import utility modules: {e}")
        return False
    
    try:
        import config
        print("✓ Configuration module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import configuration module: {e}")
        return False
    
    return True

def test_detector():
    """Test the morphology detector with a synthetic image."""
    print("\nTesting detector...")
    
    try:
        from detection import MorphologyDetector
        
        # Create a synthetic test image with some vertical lines (simulating text)
        test_image = np.zeros((200, 400, 3), dtype=np.uint8)
        
        # Add some vertical lines to simulate wagon numbers
        for x in range(50, 150, 10):
            cv2.line(test_image, (x, 50), (x, 100), (255, 255, 255), 2)
        
        for x in range(200, 300, 10):
            cv2.line(test_image, (x, 80), (x, 130), (255, 255, 255), 2)
        
        # Test detection
        detector = MorphologyDetector()
        detector.detect(test_image)
        
        rects = detector.get_rects()
        print(f"✓ Detector found {len(rects)} potential regions")
        
        # Test processed image generation
        processed = detector.get_processed_image(test_image)
        print(f"✓ Processed image shape: {processed.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Detector test failed: {e}")
        return False

def test_utilities():
    """Test utility functions."""
    print("\nTesting utilities...")
    
    try:
        from utils import convert_to_gray, concat_images, Direction
        
        # Test convert_to_gray
        color_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        gray_image = convert_to_gray(color_image)
        assert len(gray_image.shape) == 2, "Gray conversion failed"
        print("✓ convert_to_gray works correctly")
        
        # Test concat_images
        img1 = np.random.randint(0, 255, (100, 100), dtype=np.uint8)
        img2 = np.random.randint(0, 255, (100, 100), dtype=np.uint8)
        
        concat_h = concat_images(img1, img2, Direction.HORIZONTAL)
        assert concat_h.shape == (100, 200), f"Horizontal concat failed: {concat_h.shape}"
        print("✓ Horizontal concatenation works correctly")
        
        concat_v = concat_images(img1, img2, Direction.VERTICAL)
        assert concat_v.shape == (200, 100), f"Vertical concat failed: {concat_v.shape}"
        print("✓ Vertical concatenation works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Utilities test failed: {e}")
        return False

def test_video_manager():
    """Test video manager (without actually opening camera/video)."""
    print("\nTesting video manager...")
    
    try:
        from utils import VideoManager
        
        # Test initialization
        vm = VideoManager()
        print("✓ VideoManager initialized successfully")
        
        # Test video file discovery
        videos = vm.get_available_videos()
        print(f"✓ Found {len(videos)} video files in videos directory")
        
        # Test FPS calculation (should return default when no video is open)
        fps = vm.get_fps()
        assert fps > 0, "FPS should be positive"
        print(f"✓ Default FPS: {fps}")
        
        return True
        
    except Exception as e:
        print(f"✗ Video manager test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Wagons Recognition - Basic Tests")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_detector,
        test_utilities,
        test_video_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*40}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The implementation is working correctly.")
        return 0
    else:
        print("✗ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
