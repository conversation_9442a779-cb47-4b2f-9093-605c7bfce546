#!/usr/bin/env python3
"""
Start the Railway Wagon Recognition web server.
"""

import sys
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Start the web server."""
    logger.info("🚂 Starting Railway Wagon Recognition Web Server")
    logger.info("=" * 50)
    
    try:
        # Import and start the server
        import uvicorn
        from app.main import app
        
        logger.info("✅ FastAPI application loaded successfully")
        logger.info("🌐 Starting web server on http://localhost:8000")
        logger.info("📚 API documentation available at http://localhost:8000/docs")
        logger.info("🔍 Health check at http://localhost:8000/health")
        logger.info("")
        logger.info("Press Ctrl+C to stop the server")
        logger.info("=" * 50)
        
        # Start the server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        logger.info("\n🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Failed to start server: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
