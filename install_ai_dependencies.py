#!/usr/bin/env python3
"""
Install AI dependencies for the Railway Wagon Recognition System.
"""

import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_pip_install(packages, description="packages", extra_args=None):
    """Run pip install with error handling."""
    if extra_args is None:
        extra_args = []
    
    cmd = [sys.executable, "-m", "pip", "install"] + extra_args + packages
    
    logger.info(f"Installing {description}...")
    logger.info(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            logger.info(f"✅ Successfully installed {description}")
            return True
        else:
            logger.error(f"❌ Failed to install {description}")
            logger.error(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ Installation of {description} timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Error installing {description}: {e}")
        return False


def check_package(package_name):
    """Check if a package is already installed."""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False


def main():
    """Install AI dependencies step by step."""
    logger.info("🧠 Railway Wagon Recognition - AI Dependencies Installer")
    logger.info("=" * 60)
    
    success_count = 0
    total_packages = 0
    
    # Check current status
    logger.info("📋 Checking current package status...")
    packages_to_check = {
        'torch': 'PyTorch',
        'torchvision': 'TorchVision', 
        'ultralytics': 'Ultralytics YOLO',
        'easyocr': 'EasyOCR',
        'fastapi': 'FastAPI',
        'uvicorn': 'Uvicorn'
    }
    
    installed_packages = []
    missing_packages = []
    
    for package, name in packages_to_check.items():
        if check_package(package):
            logger.info(f"✅ {name} already installed")
            installed_packages.append(package)
        else:
            logger.info(f"❌ {name} not installed")
            missing_packages.append(package)
    
    if not missing_packages:
        logger.info("🎉 All AI dependencies are already installed!")
        return 0
    
    logger.info(f"\n📦 Need to install: {len(missing_packages)} packages")
    logger.info("=" * 40)
    
    # Install packages step by step
    
    # 1. PyTorch (CPU version for compatibility)
    if 'torch' in missing_packages or 'torchvision' in missing_packages:
        total_packages += 1
        logger.info("🔥 Installing PyTorch (CPU version)...")
        if run_pip_install(
            ['torch', 'torchvision'], 
            "PyTorch CPU",
            ['--index-url', 'https://download.pytorch.org/whl/cpu']
        ):
            success_count += 1
        else:
            logger.warning("⚠️  PyTorch installation failed, trying alternative...")
            if run_pip_install(['torch', 'torchvision'], "PyTorch (default)"):
                success_count += 1
    
    # 2. Ultralytics YOLO
    if 'ultralytics' in missing_packages:
        total_packages += 1
        if run_pip_install(['ultralytics'], "Ultralytics YOLO"):
            success_count += 1
    
    # 3. EasyOCR
    if 'easyocr' in missing_packages:
        total_packages += 1
        if run_pip_install(['easyocr'], "EasyOCR"):
            success_count += 1
    
    # 4. Web dependencies
    web_deps = [pkg for pkg in ['fastapi', 'uvicorn'] if pkg in missing_packages]
    if web_deps:
        total_packages += 1
        if run_pip_install(web_deps, "Web dependencies"):
            success_count += 1
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info(f"INSTALLATION RESULTS: {success_count}/{total_packages} package groups installed")
    
    if success_count == total_packages:
        logger.info("🎉 All AI dependencies installed successfully!")
        logger.info("\n🚀 Next steps:")
        logger.info("1. Download AI models: python scripts/download_models.py")
        logger.info("2. Test full system: python test_new_architecture.py")
        logger.info("3. Start web server: python start_web_server.py")
        return 0
    elif success_count > 0:
        logger.warning(f"⚠️  Partial installation: {total_packages - success_count} packages failed")
        logger.info("The system will work with fallback implementations for missing components")
        logger.info("\n🚀 You can still:")
        logger.info("1. Test current system: python test_lightweight.py")
        logger.info("2. Start web server: python start_web_server.py")
        logger.info("3. Use legacy detection: python main.py")
        return 1
    else:
        logger.error("❌ Installation failed for all packages")
        logger.info("\n🔧 Troubleshooting:")
        logger.info("1. Check internet connection")
        logger.info("2. Try manual installation: pip install torch ultralytics easyocr")
        logger.info("3. Use Docker: docker compose up --build")
        return 2


if __name__ == "__main__":
    sys.exit(main())
