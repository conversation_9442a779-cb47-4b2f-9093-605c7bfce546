#!/usr/bin/env python3
"""
Docker environment test script.
Verifies that all dependencies are properly installed in the Docker container.
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """Test Python version compatibility."""
    print("Testing Python version...")
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 8:
        print("✓ Python version is compatible")
        return True
    else:
        print("✗ Python version is not compatible (requires 3.8+)")
        return False

def test_opencv():
    """Test OpenCV installation and functionality."""
    print("\nTesting OpenCV...")
    
    try:
        import cv2
        print(f"✓ OpenCV version: {cv2.__version__}")
        
        # Test basic OpenCV functionality
        import numpy as np
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        gray = cv2.cvtColor(test_img, cv2.COLOR_BGR2GRAY)
        print("✓ OpenCV color conversion works")
        
        # Test morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        morphed = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
        print("✓ OpenCV morphological operations work")
        
        return True
        
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ OpenCV functionality test failed: {e}")
        return False

def test_numpy():
    """Test NumPy installation."""
    print("\nTesting NumPy...")
    
    try:
        import numpy as np
        print(f"✓ NumPy version: {np.__version__}")
        
        # Test basic NumPy functionality
        arr = np.random.randint(0, 255, (100, 100), dtype=np.uint8)
        print(f"✓ NumPy array creation works: {arr.shape}")
        
        return True
        
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ NumPy functionality test failed: {e}")
        return False

def test_application_imports():
    """Test application module imports."""
    print("\nTesting application imports...")
    
    try:
        # Add current directory to path
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        from detection import Detector, MorphologyDetector
        print("✓ Detection modules imported successfully")
        
        from utils import VideoManager, convert_to_gray, concat_images
        print("✓ Utility modules imported successfully")
        
        import config
        print("✓ Configuration module imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Application import failed: {e}")
        return False

def test_file_permissions():
    """Test file system permissions."""
    print("\nTesting file permissions...")
    
    try:
        # Test read permissions
        current_dir = Path(__file__).parent
        if current_dir.exists():
            print("✓ Can read current directory")
        
        # Test videos directory
        videos_dir = current_dir / "videos"
        if videos_dir.exists():
            print("✓ Videos directory exists")
            
            # Test write permissions
            test_file = videos_dir / "test_write.tmp"
            try:
                test_file.write_text("test")
                test_file.unlink()
                print("✓ Can write to videos directory")
            except Exception as e:
                print(f"⚠ Cannot write to videos directory: {e}")
        else:
            print("⚠ Videos directory does not exist")
        
        return True
        
    except Exception as e:
        print(f"✗ File permission test failed: {e}")
        return False

def test_display_environment():
    """Test display environment variables."""
    print("\nTesting display environment...")
    
    display = os.environ.get('DISPLAY')
    if display:
        print(f"✓ DISPLAY environment variable set: {display}")
    else:
        print("⚠ DISPLAY environment variable not set (GUI may not work)")
    
    pythonpath = os.environ.get('PYTHONPATH')
    if pythonpath:
        print(f"✓ PYTHONPATH environment variable set: {pythonpath}")
    else:
        print("⚠ PYTHONPATH environment variable not set")
    
    return True

def test_camera_devices():
    """Test camera device availability."""
    print("\nTesting camera devices...")
    
    # Check for video devices (Linux)
    video_devices = list(Path("/dev").glob("video*")) if Path("/dev").exists() else []
    
    if video_devices:
        print(f"✓ Found video devices: {[str(d) for d in video_devices]}")
    else:
        print("⚠ No video devices found (camera functionality may not work)")
    
    return True

def main():
    """Run all Docker environment tests."""
    print("Docker Environment Test Suite")
    print("=" * 40)
    
    tests = [
        test_python_version,
        test_numpy,
        test_opencv,
        test_application_imports,
        test_file_permissions,
        test_display_environment,
        test_camera_devices
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*40}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ Docker environment is properly configured!")
        return 0
    elif passed >= total - 2:  # Allow for camera/display warnings
        print("✓ Docker environment is mostly configured (some warnings)")
        return 0
    else:
        print("✗ Docker environment has issues that need to be resolved")
        return 1

if __name__ == "__main__":
    sys.exit(main())
