"""
Object Detector for Railway Wagon Recognition

Implements YOLO v8 based object detection to identify:
- wagons: Railway wagon objects
- numbers: Number regions on wagon sides

Replaces the basic morphological detection with modern deep learning.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

import cv2
import numpy as np

from app.core.config import settings

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    logging.warning("Ultralytics YOLO not available. Install with: pip install ultralytics")


class WagonDetector:
    """
    YOLO v8 based object detector for wagons and number regions.
    
    Detects:
    - Railway wagons in video frames
    - Number regions on wagon sides
    - Provides bounding boxes and confidence scores
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.class_names = ['wagon', 'number']
        
        if not YOLO_AVAILABLE:
            self.logger.warning("YOLO not available - using mock detector")
    
    def load_model(self, model_path: Optional[Path] = None) -> bool:
        """
        Load the YOLO v8 model.
        
        Args:
            model_path: Optional path to custom model file
            
        Returns:
            True if model loaded successfully, False otherwise
        """
        if not YOLO_AVAILABLE:
            self.logger.info("Using mock detector (YOLO not available)")
            return True
        
        try:
            if model_path is None:
                model_path = settings.MODELS_DIR / settings.YOLO_MODEL
            
            if model_path.exists():
                # Load custom trained model
                self.logger.info(f"Loading custom YOLO model from: {model_path}")
                self.model = YOLO(str(model_path))
            else:
                # Use pre-trained YOLO model as fallback
                self.logger.warning(f"Custom model not found: {model_path}")
                self.logger.info("Using pre-trained YOLOv8n model")
                self.model = YOLO('yolov8n.pt')  # Nano version for speed
            
            # Warm up the model
            dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
            _ = self.model(dummy_image, verbose=False)
            
            self.logger.info("YOLO model loaded and warmed up successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load YOLO model: {e}")
            return False
    
    def detect(self, frame: np.ndarray, confidence_threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Detect objects in a video frame.
        
        Args:
            frame: Input video frame
            confidence_threshold: Optional confidence threshold override
            
        Returns:
            List of detection dictionaries with bbox, confidence, class
        """
        if confidence_threshold is None:
            confidence_threshold = settings.YOLO_CONFIDENCE
        
        if not YOLO_AVAILABLE or self.model is None:
            return self._mock_detect(frame, confidence_threshold)
        
        try:
            # Run YOLO inference
            results = self.model(frame, conf=confidence_threshold, verbose=False)
            
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Extract box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        # Map class ID to name
                        if hasattr(result, 'names') and class_id in result.names:
                            class_name = result.names[class_id]
                        else:
                            # For custom models, map to our classes
                            class_name = self.class_names[class_id] if class_id < len(self.class_names) else 'unknown'
                        
                        detection = {
                            'bbox': (int(x1), int(y1), int(x2), int(y2)),
                            'confidence': float(confidence),
                            'class': class_name,
                            'class_id': class_id
                        }
                        
                        detections.append(detection)
            
            return detections
            
        except Exception as e:
            self.logger.error(f"Error during YOLO detection: {e}")
            return []
    
    def detect_batch(self, frames: List[np.ndarray], confidence_threshold: Optional[float] = None) -> List[List[Dict[str, Any]]]:
        """
        Detect objects in multiple frames (batch processing).
        
        Args:
            frames: List of video frames
            confidence_threshold: Optional confidence threshold override
            
        Returns:
            List of detection lists (one per frame)
        """
        if not frames:
            return []
        
        if not YOLO_AVAILABLE or self.model is None:
            return [self._mock_detect(frame, confidence_threshold) for frame in frames]
        
        if confidence_threshold is None:
            confidence_threshold = settings.YOLO_CONFIDENCE
        
        try:
            # Run batch inference
            results = self.model(frames, conf=confidence_threshold, verbose=False)
            
            batch_detections = []
            
            for result in results:
                frame_detections = []
                boxes = result.boxes
                
                if boxes is not None:
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        if hasattr(result, 'names') and class_id in result.names:
                            class_name = result.names[class_id]
                        else:
                            class_name = self.class_names[class_id] if class_id < len(self.class_names) else 'unknown'
                        
                        detection = {
                            'bbox': (int(x1), int(y1), int(x2), int(y2)),
                            'confidence': float(confidence),
                            'class': class_name,
                            'class_id': class_id
                        }
                        
                        frame_detections.append(detection)
                
                batch_detections.append(frame_detections)
            
            return batch_detections
            
        except Exception as e:
            self.logger.error(f"Error during batch YOLO detection: {e}")
            return [[] for _ in frames]
    
    def _mock_detect(self, frame: np.ndarray, confidence_threshold: float) -> List[Dict[str, Any]]:
        """
        Mock detection for development/testing when YOLO is not available.
        
        Uses simple computer vision techniques to simulate object detection.
        """
        try:
            detections = []
            
            # Convert to grayscale
            if len(frame.shape) == 3:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = frame
            
            height, width = gray.shape
            
            # Simple edge-based detection (similar to original morphological approach)
            edges = cv2.Canny(gray, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                # Get bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                
                # Filter by size and aspect ratio
                if area < 500 or area > width * height * 0.5:
                    continue
                
                aspect_ratio = w / h if h > 0 else 0
                
                # Classify based on aspect ratio and size
                if aspect_ratio > 2.0 and area > 2000:
                    # Wide rectangle - likely a wagon
                    detection = {
                        'bbox': (x, y, x + w, y + h),
                        'confidence': min(0.9, area / 10000),  # Mock confidence based on size
                        'class': 'wagon',
                        'class_id': 0
                    }
                    detections.append(detection)
                    
                elif 1.5 <= aspect_ratio <= 6.0 and 500 <= area <= 5000:
                    # Medium rectangle with text-like aspect ratio - likely a number
                    detection = {
                        'bbox': (x, y, x + w, y + h),
                        'confidence': min(0.8, aspect_ratio / 6.0),  # Mock confidence based on aspect ratio
                        'class': 'number',
                        'class_id': 1
                    }
                    detections.append(detection)
            
            # Filter by confidence threshold
            detections = [d for d in detections if d['confidence'] >= confidence_threshold]
            
            return detections
            
        except Exception as e:
            self.logger.error(f"Error in mock detection: {e}")
            return []
    
    def visualize_detections(self, frame: np.ndarray, detections: List[Dict[str, Any]]) -> np.ndarray:
        """
        Draw detection results on the frame for visualization.
        
        Args:
            frame: Input frame
            detections: List of detections
            
        Returns:
            Frame with drawn bounding boxes and labels
        """
        result_frame = frame.copy()
        
        colors = {
            'wagon': (0, 255, 0),    # Green for wagons
            'number': (0, 0, 255),   # Red for numbers
            'unknown': (255, 0, 0)   # Blue for unknown
        }
        
        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            class_name = detection['class']
            
            x1, y1, x2, y2 = bbox
            color = colors.get(class_name, (255, 255, 255))
            
            # Draw bounding box
            cv2.rectangle(result_frame, (x1, y1), (x2, y2), color, 2)
            
            # Draw label
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
            
            # Background for label
            cv2.rectangle(result_frame, 
                         (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), 
                         color, -1)
            
            # Label text
            cv2.putText(result_frame, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return result_frame
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        if not YOLO_AVAILABLE:
            return {
                'status': 'mock',
                'reason': 'YOLO not available',
                'classes': self.class_names
            }
        
        if self.model is None:
            return {'status': 'not_loaded'}
        
        return {
            'status': 'loaded',
            'architecture': 'YOLO v8',
            'classes': self.class_names,
            'model_type': str(type(self.model)),
            'expected_fps': '10+ on CPU'
        }
