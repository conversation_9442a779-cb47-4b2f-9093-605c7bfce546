"""
AI Pipeline for Railway Wagon Recognition

This module contains the core AI components:
- Scene Classification (ResNet-18)
- Object Detection (YOLO v8)
- OCR Engine (EasyOCR)
- Number Validation
- Object Tracking
"""

from .pipeline import WagonRecognition<PERSON>ipeline
from .scene_classifier import SceneClassifier
from .object_detector import WagonDetector
from .ocr_engine import NumberOCR
from .validator import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .tracker import WagonTracker

__all__ = [
    'WagonRecognitionPipeline',
    'SceneClassifier',
    'WagonDetector', 
    'NumberOCR',
    'NumberValidator',
    'WagonTracker'
]
