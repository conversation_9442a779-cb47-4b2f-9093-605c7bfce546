"""
Video handling utilities.
"""

from pathlib import Path
from typing import List, Optional, Union
import cv2
import numpy as np

from config import (
    DEFAULT_VIDEO_PATH, 
    VIDEO_EXTENSIONS, 
    SAMPLE_VIDEO_FILES,
    DEFAULT_CAMERA_INDEX
)


class VideoManager:
    """
    Manages video input from files or camera.
    """
    
    def __init__(self):
        self.capture: Optional[cv2.VideoCapture] = None
        self.is_camera: bool = False
        self.video_path: Optional[Path] = None
        
    def open_camera(self, camera_index: int = DEFAULT_CAMERA_INDEX) -> bool:
        """
        Open camera for video capture.
        
        Args:
            camera_index: Camera index (default: 0)
            
        Returns:
            True if camera opened successfully, False otherwise
        """
        self.capture = cv2.VideoCapture(camera_index)
        if self.capture.isOpened():
            self.is_camera = True
            self.video_path = None
            return True
        else:
            self.capture = None
            return False
    
    def open_video_file(self, video_path: Union[str, Path]) -> bool:
        """
        Open video file for playback.
        
        Args:
            video_path: Path to video file
            
        Returns:
            True if video opened successfully, False otherwise
        """
        video_path = Path(video_path)
        
        if not video_path.exists():
            print(f"Video file not found: {video_path}")
            return False
        
        self.capture = cv2.VideoCapture(str(video_path))
        if self.capture.isOpened():
            self.is_camera = False
            self.video_path = video_path
            return True
        else:
            self.capture = None
            return False
    
    def get_available_videos(self, video_dir: Optional[Path] = None) -> List[Path]:
        """
        Get list of available video files.
        
        Args:
            video_dir: Directory to search for videos (default: DEFAULT_VIDEO_PATH)
            
        Returns:
            List of video file paths
        """
        if video_dir is None:
            video_dir = DEFAULT_VIDEO_PATH
        
        video_files = []
        
        if video_dir.exists() and video_dir.is_dir():
            for ext in VIDEO_EXTENSIONS:
                video_files.extend(video_dir.glob(f"*{ext}"))
        
        # Add sample files if they exist
        for sample_file in SAMPLE_VIDEO_FILES:
            sample_path = video_dir / sample_file
            if sample_path.exists():
                video_files.append(sample_path)
        
        return sorted(video_files)
    
    def choose_video_file(self) -> Optional[Path]:
        """
        Interactive video file selection.
        
        Returns:
            Selected video file path or None if cancelled
        """
        available_videos = self.get_available_videos()
        
        if not available_videos:
            print("No video files found in the videos directory.")
            print(f"Please add video files to: {DEFAULT_VIDEO_PATH}")
            return None
        
        print("\nAvailable video files:")
        for i, video_path in enumerate(available_videos, 1):
            print(f"{i}: {video_path.name}")
        
        while True:
            try:
                choice = input(f"\nEnter video number (1-{len(available_videos)}) or 'q' to quit: ")
                
                if choice.lower() == 'q':
                    return None
                
                video_index = int(choice) - 1
                
                if 0 <= video_index < len(available_videos):
                    return available_videos[video_index]
                else:
                    print(f"Please enter a number between 1 and {len(available_videos)}")
                    
            except ValueError:
                print("Please enter a valid number or 'q' to quit")
    
    def get_fps(self) -> float:
        """
        Get frames per second of the current video.
        
        Returns:
            FPS value or 30.0 as default
        """
        if self.capture is not None:
            fps = self.capture.get(cv2.CAP_PROP_FPS)
            return fps if fps > 0 else 30.0
        return 30.0
    
    def get_frame_count(self) -> int:
        """
        Get total frame count of the current video.
        
        Returns:
            Total frame count or 0 for camera
        """
        if self.capture is not None and not self.is_camera:
            return int(self.capture.get(cv2.CAP_PROP_FRAME_COUNT))
        return 0
    
    def set_frame_position(self, frame_number: int) -> bool:
        """
        Set current frame position (video files only).
        
        Args:
            frame_number: Frame number to seek to
            
        Returns:
            True if successful, False otherwise
        """
        if self.capture is not None and not self.is_camera:
            return self.capture.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        return False
    
    def read_frame(self) -> tuple[bool, Optional[np.ndarray]]:
        """
        Read next frame from video source.
        
        Returns:
            Tuple of (success, frame) where success is bool and frame is numpy array
        """
        if self.capture is not None:
            return self.capture.read()
        return False, None
    
    def release(self) -> None:
        """Release video capture resources."""
        if self.capture is not None:
            self.capture.release()
            self.capture = None
        self.is_camera = False
        self.video_path = None
