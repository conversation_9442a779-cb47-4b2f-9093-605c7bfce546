"""
Morphological detection implementation for wagon number recognition.
"""

from typing import Tuple
import cv2
import numpy as np

from .detector import Detector
from config import (
    get_blur_size,
    MORPH_KERNEL_SIZE,
    MIN_CONTOUR_AREA,
    MAX_CONTOUR_AREA,
    MIN_ASPECT_RATIO,
    MAX_ASPECT_RATIO,
    DEBUG_MODE
)


class MorphologyDetector(Detector):
    """
    Morphological detector for wagon numbers using OpenCV morphological operations.
    """
    
    def detect(self, input_image: np.ndarray) -> None:
        """
        Detect wagon numbers using morphological operations.
        
        Args:
            input_image: Input image as numpy array
        """
        # Clear previous detections
        self.clear_rects()
        
        # Perform morphological detection
        processed_image = self.morph_detect(input_image)
        
        # Find contours of possible plates
        contours, _ = cv2.findContours(
            processed_image, 
            cv2.RETR_EXTERNAL, 
            cv2.CHAIN_APPROX_SIMPLE
        )
        
        # Process each contour
        for contour in contours:
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            rect = (x, y, w, h)

            # Verify if the rectangle size is valid
            if self.verify_size(rect):
                self._rects.append(rect)
                if DEBUG_MODE:
                    print(f"Valid detection: x={x}, y={y}, w={w}, h={h}, area={w*h}, aspect={w/h:.2f}")
            elif DEBUG_MODE:
                print(f"Rejected: x={x}, y={y}, w={w}, h={h}, area={w*h}, aspect={w/h:.2f}")
    
    def morph_detect(self, input_image: np.ndarray) -> np.ndarray:
        """
        Perform morphological detection on the input image.
        
        Args:
            input_image: Input image as numpy array
            
        Returns:
            Processed binary image
        """
        # Convert to grayscale if needed
        if len(input_image.shape) == 3:
            gray = cv2.cvtColor(input_image, cv2.COLOR_BGR2GRAY)
        else:
            gray = input_image.copy()
        
        # Apply blur to reduce noise
        blur_size = get_blur_size()
        if blur_size > 1:
            gray = cv2.blur(gray, (blur_size, blur_size))
        
        # Find vertical lines using Sobel operator
        # Wagon numbers have high density of vertical lines
        sobel = cv2.Sobel(gray, cv2.CV_8U, 1, 0, ksize=3, scale=1, delta=0)
        
        # Apply threshold using Otsu's method
        _, threshold = cv2.threshold(
            sobel, 
            0, 
            255, 
            cv2.THRESH_OTSU + cv2.THRESH_BINARY
        )
        
        # Morphological closing to connect nearby regions
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, MORPH_KERNEL_SIZE)
        morphed = cv2.morphologyEx(threshold, cv2.MORPH_CLOSE, kernel)
        
        return morphed
    
    def get_processed_image(self, input_image: np.ndarray) -> np.ndarray:
        """
        Get the processed image for debugging/visualization.
        
        Args:
            input_image: Input image as numpy array
            
        Returns:
            Processed binary image
        """
        return self.morph_detect(input_image)
