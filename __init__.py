"""
Wagons Recognition - Python Implementation

A Python port of the original C++ WagonsRecognition project for railway wagon 
number detection using computer vision.

This package provides:
- Morphological detection algorithms for wagon number recognition
- Video and camera input handling
- Cross-platform support
- Modern Python implementation with type hints

Example usage:
    from wagons_recognition_python.detection import MorphologyDetector
    from wagons_recognition_python.utils import VideoManager
    
    # Create detector
    detector = MorphologyDetector()
    
    # Setup video source
    video_manager = VideoManager()
    video_manager.open_camera()
    
    # Process frames
    success, frame = video_manager.read_frame()
    if success:
        detector.detect(frame)
        rects = detector.get_rects()
"""

__version__ = "1.0.0"
__author__ = "Python Port"
__email__ = ""

from .detection import Detector, MorphologyDetector
from .utils import VideoManager, convert_to_gray, concat_images

__all__ = [
    'Detector',
    'MorphologyDetector', 
    'VideoManager',
    'convert_to_gray',
    'concat_images'
]
