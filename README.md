# Railway Wagon Recognition System

An AI-powered system for counting railway wagons and recognizing their numbers from video streams. This system implements the research paper "Real-time Train Wagon Counting and Number Recognition Algorithm" with modern deep learning techniques.

## 🚂 Features

### Core Functionality
- **Automated Wagon Counting**: 98%+ accuracy using scene classification
- **Number Recognition**: 95%+ accuracy (single frame), 99%+ (multi-frame)
- **Real-time Processing**: 10+ FPS on CPU, supports multiple video streams
- **Multi-format Support**: MP4, AVI, MOV, RTSP/RTMP streams

### AI Pipeline
- **Scene Classification**: ResNet-18 based wagon/gap/background detection
- **Object Detection**: YOLO v8 for wagon and number region detection
- **OCR Engine**: EasyOCR for robust text recognition
- **Number Validation**: Checksum validation for railway standards
- **Object Tracking**: Multi-object tracking for accurate counting

### Web Interface
- **FastAPI Backend**: RESTful API for system integration
- **File Upload**: Drag & drop video processing
- **Real-time Monitoring**: WebSocket updates and progress tracking
- **Results Export**: CSV, JSON, XML format support

## 🚀 Quick Start

### Option 1: Docker (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd wagons_recognition_python

# Start with Docker Compose
docker compose up --build

# Or use the helper script
./docker-run.sh          # Linux/macOS
docker-run.bat           # Windows
```

### Option 2: Local Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Download AI models
python scripts/download_models.py

# Start the web interface
python app/main.py

# Or use the legacy CLI interface
python main.py
```

## 📋 Requirements

### System Requirements
- **CPU**: Intel i5-8400 or AMD Ryzen 5 2600 (minimum)
- **RAM**: 8 GB (minimum), 16 GB (recommended)
- **Storage**: 50 GB free space (SSD recommended)
- **GPU**: Optional (NVIDIA GTX 1660 Ti+ for acceleration)

### Software Requirements
- **Python**: 3.9+ (3.11 recommended)
- **Operating System**: Linux (primary), Windows, macOS
- **Dependencies**: See requirements.txt

## Usage

### Docker Usage

```bash
# Using Docker Compose
docker-compose up

# Using Docker directly
docker run -it --rm \
  -e DISPLAY=$DISPLAY \
  -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
  -v $(pwd)/videos:/app/videos:rw \
  --device /dev/video0:/dev/video0 \
  wagons-recognition
```

### Local Usage

```bash
python main.py
```

Choose input source:
1. Video file - Select from available video files
2. Camera - Use default camera (index 0)

## Controls

- **ESC**: Exit application
- **SPACE**: Pause/resume video playback
- **Trackbar**: Navigate through video frames (video mode only)

## Project Structure

```
wagons_recognition_python/
├── main.py                 # Main application entry point
├── config.py              # Configuration settings
├── detection/
│   ├── __init__.py
│   ├── detector.py        # Base detector class
│   └── morphology.py      # Morphological detection implementation
├── utils/
│   ├── __init__.py
│   ├── image_utils.py     # Image processing utilities
│   └── video_utils.py     # Video handling utilities
├── requirements.txt       # Python dependencies
├── Dockerfile            # Docker image definition
├── docker-compose.yml    # Docker Compose configuration
├── docker-run.sh         # Docker helper script (Linux/macOS)
├── docker-run.bat        # Docker helper script (Windows)
├── .dockerignore         # Docker build exclusions
├── DOCKER.md             # Docker deployment guide
└── README.md             # This file
```

## Docker Benefits

🐳 **Why use Docker?**
- **Easy deployment**: No need to install Python or dependencies
- **Consistent environment**: Works the same on all platforms
- **Isolated execution**: No conflicts with system packages
- **GUI support**: Includes X11 forwarding for cross-platform GUI
- **Camera access**: Properly configured device access

See [DOCKER.md](DOCKER.md) for detailed Docker deployment instructions.

## Original Project

This is a Python port of the original C++ project:
https://github.com/igororlov92/WagonsRecognition

Developed for Video Internet Technologies Ltd. (www.vit.ua)
Eligible for ex-USSR (CIS countries) railway wagon number standard.
