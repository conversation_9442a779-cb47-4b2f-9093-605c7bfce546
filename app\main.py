"""
FastAPI application for Railway Wagon Recognition System
"""

import logging
import sys
from pathlib import Path

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.config import settings
from ai.pipeline import WagonRecognitionPipeline

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)

logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="AI-powered railway wagon counting and number recognition system"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global AI pipeline instance
pipeline = None


@app.on_event("startup")
async def startup_event():
    """Initialize the AI pipeline on startup."""
    global pipeline
    
    logger.info("Starting Railway Wagon Recognition System")
    logger.info(f"Version: {settings.APP_VERSION}")
    
    # Initialize AI pipeline
    pipeline = WagonRecognitionPipeline()
    
    if pipeline.initialize():
        logger.info("AI pipeline initialized successfully")
    else:
        logger.error("Failed to initialize AI pipeline")
        # Continue anyway for development/testing


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logger.info("Shutting down Railway Wagon Recognition System")


@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with basic information."""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>{settings.APP_NAME}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .header {{ color: #2c3e50; }}
            .info {{ background: #ecf0f1; padding: 20px; border-radius: 5px; }}
            .status {{ color: #27ae60; }}
            .error {{ color: #e74c3c; }}
        </style>
    </head>
    <body>
        <h1 class="header">{settings.APP_NAME}</h1>
        <div class="info">
            <h2>System Information</h2>
            <p><strong>Version:</strong> {settings.APP_VERSION}</p>
            <p><strong>Status:</strong> <span class="status">Running</span></p>
            <p><strong>AI Pipeline:</strong> {'Initialized' if pipeline and pipeline.scene_classifier else 'Not Available'}</p>
        </div>
        
        <h2>Available Endpoints</h2>
        <ul>
            <li><a href="/docs">API Documentation (Swagger)</a></li>
            <li><a href="/health">Health Check</a></li>
            <li><a href="/api/v1/system/info">System Information</a></li>
            <li><a href="/api/v1/models/info">AI Models Information</a></li>
        </ul>
        
        <h2>Quick Start</h2>
        <p>To process a video file, use the API endpoints or upload through the web interface.</p>
        <p>For development, you can also run the legacy CLI interface:</p>
        <pre>python main.py</pre>
    </body>
    </html>
    """
    return html_content


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "version": settings.APP_VERSION,
        "pipeline_initialized": pipeline is not None and pipeline.scene_classifier is not None
    }


@app.get("/api/v1/system/info")
async def get_system_info():
    """Get system information."""
    return {
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "debug_mode": settings.DEBUG,
        "max_concurrent_jobs": settings.MAX_CONCURRENT_JOBS,
        "supported_formats": settings.SUPPORTED_VIDEO_FORMATS,
        "ai_parameters": {
            "target_fps": settings.TARGET_FPS,
            "scene_classifier_confidence": settings.SCENE_CLASSIFIER_CONFIDENCE,
            "yolo_confidence": settings.YOLO_CONFIDENCE,
            "ocr_confidence": settings.OCR_CONFIDENCE
        }
    }


@app.get("/api/v1/models/info")
async def get_models_info():
    """Get AI models information."""
    if not pipeline:
        raise HTTPException(status_code=503, detail="AI pipeline not initialized")
    
    models_info = {}
    
    # Scene classifier info
    if pipeline.scene_classifier:
        models_info["scene_classifier"] = pipeline.scene_classifier.get_model_info()
    
    # Object detector info
    if pipeline.object_detector:
        models_info["object_detector"] = pipeline.object_detector.get_model_info()
    
    # OCR engine info
    if pipeline.ocr_engine:
        models_info["ocr_engine"] = pipeline.ocr_engine.get_engine_info()
    
    # Number validator info
    if pipeline.number_validator:
        models_info["number_validator"] = pipeline.number_validator.get_validation_stats()
    
    # Wagon tracker info
    if pipeline.wagon_tracker:
        models_info["wagon_tracker"] = pipeline.wagon_tracker.get_tracking_stats()
    
    return models_info


@app.get("/api/v1/performance/metrics")
async def get_performance_metrics():
    """Get current performance metrics."""
    if not pipeline:
        raise HTTPException(status_code=503, detail="AI pipeline not initialized")
    
    return pipeline.get_performance_metrics()


# Mount static files (for future web interface)
static_dir = project_root / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")


if __name__ == "__main__":
    import uvicorn
    
    logger.info("Starting development server")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
