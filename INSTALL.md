# Installation Guide

## Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- A webcam or video files containing railway wagons

## Installation Steps

### 1. Clone or Download the Project

```bash
# If using git
git clone <repository-url>
cd wagons_recognition_python

# Or download and extract the ZIP file
```

### 2. Create Virtual Environment (Recommended)

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Verify Installation

```bash
python -c "import cv2; print('OpenCV version:', cv2.__version__)"
```

## Alternative Installation Methods

### Using pip (if package is published)

```bash
pip install wagons-recognition-python
```

### Development Installation

```bash
pip install -e .
```

## Troubleshooting

### OpenCV Installation Issues

If you encounter issues with OpenCV installation:

```bash
# Try installing with conda
conda install opencv

# Or use a specific OpenCV package
pip install opencv-python-headless  # For headless systems
```

### Camera Access Issues

- **Windows**: Ensure camera permissions are enabled for Python applications
- **Linux**: You may need to add your user to the `video` group:
  ```bash
  sudo usermod -a -G video $USER
  ```
- **macOS**: Grant camera permissions when prompted

### Video File Issues

- Ensure video files are in supported formats (.avi, .mp4, .mov, .mkv)
- Place video files in the `videos/` directory
- Check that video files are not corrupted

## System Requirements

- **RAM**: Minimum 4GB, recommended 8GB+
- **CPU**: Any modern processor (multi-core recommended for better performance)
- **Storage**: At least 100MB free space for the application
- **Camera**: USB webcam or built-in camera (for camera mode)

## Performance Tips

- Use smaller video resolutions for faster processing
- Close other applications that might use the camera
- For better performance, use SSD storage for video files
