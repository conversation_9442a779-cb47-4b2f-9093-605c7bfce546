"""
Main AI Pipeline for Railway Wagon Recognition

Implements the complete processing pipeline:
Scene Classification → Object Detection → OCR → Validation → Tracking
"""

import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path

import cv2
import numpy as np

from app.core.config import settings


@dataclass
class DetectionResult:
    """Result from a single frame detection."""
    frame_number: int
    timestamp: float
    scene_type: str  # 'wagon', 'gap', 'background'
    scene_confidence: float
    wagons: List[Dict[str, Any]]  # List of detected wagons with bounding boxes
    numbers: List[Dict[str, Any]]  # List of recognized numbers with confidence
    processing_time: float


@dataclass
class WagonCount:
    """Wagon counting result."""
    total_wagons: int
    wagon_numbers: List[str]
    confidence_scores: List[float]
    processing_stats: Dict[str, Any]


class WagonRecognitionPipeline:
    """
    Main pipeline for wagon recognition and counting.
    
    This class orchestrates the entire AI pipeline from video input
    to final wagon count and number recognition results.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.scene_classifier = None
        self.object_detector = None
        self.ocr_engine = None
        self.number_validator = None
        self.wagon_tracker = None
        
        # Processing state
        self.frame_buffer = []
        self.detection_history = []
        self.wagon_count = 0
        self.recognized_numbers = []
        
        # Performance metrics
        self.total_frames_processed = 0
        self.total_processing_time = 0.0
        
    def initialize(self) -> bool:
        """
        Initialize all AI components.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            self.logger.info("Initializing AI pipeline components...")
            
            # Initialize scene classifier
            from .scene_classifier import SceneClassifier
            self.scene_classifier = SceneClassifier()
            if not self.scene_classifier.load_model():
                self.logger.error("Failed to load scene classifier")
                return False
            
            # Initialize object detector
            from .object_detector import WagonDetector
            self.object_detector = WagonDetector()
            if not self.object_detector.load_model():
                self.logger.error("Failed to load object detector")
                return False
            
            # Initialize OCR engine
            from .ocr_engine import NumberOCR
            self.ocr_engine = NumberOCR()
            if not self.ocr_engine.initialize():
                self.logger.error("Failed to initialize OCR engine")
                return False
            
            # Initialize number validator
            from .validator import NumberValidator
            self.number_validator = NumberValidator()
            
            # Initialize wagon tracker
            from .tracker import WagonTracker
            self.wagon_tracker = WagonTracker()
            
            self.logger.info("AI pipeline initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AI pipeline: {e}")
            return False
    
    def process_frame(self, frame: np.ndarray, frame_number: int) -> DetectionResult:
        """
        Process a single video frame through the AI pipeline.
        
        Args:
            frame: Input video frame
            frame_number: Frame sequence number
            
        Returns:
            DetectionResult with all processing results
        """
        start_time = time.time()
        
        # Step 1: Scene Classification
        scene_type, scene_confidence = self.scene_classifier.classify(frame)
        
        wagons = []
        numbers = []
        
        # Step 2: Object Detection (only for wagon frames)
        if scene_type == 'wagon' and scene_confidence > settings.SCENE_CLASSIFIER_CONFIDENCE:
            # Detect wagons and number regions
            detections = self.object_detector.detect(frame)
            
            for detection in detections:
                if detection['class'] == 'wagon':
                    wagons.append(detection)
                elif detection['class'] == 'number':
                    # Step 3: OCR Processing
                    number_region = self._extract_region(frame, detection['bbox'])
                    ocr_results = self.ocr_engine.recognize(number_region)
                    
                    for ocr_result in ocr_results:
                        # Step 4: Number Validation
                        if self.number_validator.validate(ocr_result['text']):
                            numbers.append({
                                'text': ocr_result['text'],
                                'confidence': ocr_result['confidence'],
                                'bbox': detection['bbox'],
                                'validated': True
                            })
        
        processing_time = time.time() - start_time
        
        # Update statistics
        self.total_frames_processed += 1
        self.total_processing_time += processing_time
        
        result = DetectionResult(
            frame_number=frame_number,
            timestamp=time.time(),
            scene_type=scene_type,
            scene_confidence=scene_confidence,
            wagons=wagons,
            numbers=numbers,
            processing_time=processing_time
        )
        
        # Add to detection history
        self.detection_history.append(result)
        
        return result
    
    def process_video(self, video_path: Path, progress_callback=None) -> WagonCount:
        """
        Process an entire video file.
        
        Args:
            video_path: Path to video file
            progress_callback: Optional callback for progress updates
            
        Returns:
            WagonCount with final results
        """
        self.logger.info(f"Processing video: {video_path}")
        
        # Reset state
        self._reset_state()
        
        # Open video
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        self.logger.info(f"Video info: {total_frames} frames, {fps:.2f} FPS")
        
        frame_number = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Process frame
                result = self.process_frame(frame, frame_number)
                
                # Update progress
                if progress_callback:
                    progress = (frame_number + 1) / total_frames
                    progress_callback(progress, result)
                
                frame_number += 1
                
                # Log progress periodically
                if frame_number % 100 == 0:
                    avg_fps = frame_number / self.total_processing_time if self.total_processing_time > 0 else 0
                    self.logger.info(f"Processed {frame_number}/{total_frames} frames, avg {avg_fps:.1f} FPS")
        
        finally:
            cap.release()
        
        # Step 5: Final wagon counting and tracking
        final_count = self._finalize_counting()
        
        self.logger.info(f"Processing complete: {final_count.total_wagons} wagons, {len(final_count.wagon_numbers)} numbers recognized")
        
        return final_count
    
    def _extract_region(self, frame: np.ndarray, bbox: Tuple[int, int, int, int]) -> np.ndarray:
        """Extract a region from the frame based on bounding box."""
        x1, y1, x2, y2 = bbox
        return frame[y1:y2, x1:x2]
    
    def _reset_state(self):
        """Reset pipeline state for new processing."""
        self.frame_buffer.clear()
        self.detection_history.clear()
        self.wagon_count = 0
        self.recognized_numbers.clear()
        self.total_frames_processed = 0
        self.total_processing_time = 0.0
    
    def _finalize_counting(self) -> WagonCount:
        """
        Finalize wagon counting based on detection history.
        
        Uses the wagon tracker to determine final count and
        consolidate number recognition results.
        """
        # Use tracker to count unique wagons
        wagon_count = self.wagon_tracker.count_wagons(self.detection_history)
        
        # Consolidate number recognition results
        consolidated_numbers = self._consolidate_numbers()
        
        # Calculate processing statistics
        avg_processing_time = self.total_processing_time / max(1, self.total_frames_processed)
        avg_fps = self.total_frames_processed / max(0.001, self.total_processing_time)
        
        processing_stats = {
            'total_frames': self.total_frames_processed,
            'total_processing_time': self.total_processing_time,
            'average_processing_time_per_frame': avg_processing_time,
            'average_fps': avg_fps,
            'scene_classification_accuracy': self._calculate_scene_accuracy(),
            'detection_rate': self._calculate_detection_rate()
        }
        
        return WagonCount(
            total_wagons=wagon_count,
            wagon_numbers=[num['text'] for num in consolidated_numbers],
            confidence_scores=[num['confidence'] for num in consolidated_numbers],
            processing_stats=processing_stats
        )
    
    def _consolidate_numbers(self) -> List[Dict[str, Any]]:
        """Consolidate number recognition results from multiple frames."""
        # Group numbers by similarity and choose best confidence
        number_groups = {}
        
        for result in self.detection_history:
            for number in result.numbers:
                text = number['text']
                confidence = number['confidence']
                
                # Find similar existing numbers
                found_group = None
                for existing_text in number_groups:
                    if self._numbers_similar(text, existing_text):
                        found_group = existing_text
                        break
                
                if found_group:
                    # Update if higher confidence
                    if confidence > number_groups[found_group]['confidence']:
                        number_groups[found_group] = {
                            'text': text,
                            'confidence': confidence,
                            'count': number_groups[found_group]['count'] + 1
                        }
                    else:
                        number_groups[found_group]['count'] += 1
                else:
                    # New number group
                    number_groups[text] = {
                        'text': text,
                        'confidence': confidence,
                        'count': 1
                    }
        
        # Return consolidated numbers sorted by confidence
        consolidated = list(number_groups.values())
        consolidated.sort(key=lambda x: x['confidence'], reverse=True)
        
        return consolidated
    
    def _numbers_similar(self, num1: str, num2: str, threshold: float = 0.8) -> bool:
        """Check if two numbers are similar (for consolidation)."""
        if len(num1) != len(num2):
            return False
        
        matches = sum(1 for a, b in zip(num1, num2) if a == b)
        similarity = matches / len(num1)
        
        return similarity >= threshold
    
    def _calculate_scene_accuracy(self) -> float:
        """Calculate scene classification accuracy estimate."""
        # This is a simplified estimate - in production you'd have ground truth
        total_classifications = len(self.detection_history)
        if total_classifications == 0:
            return 0.0
        
        high_confidence_classifications = sum(
            1 for result in self.detection_history 
            if result.scene_confidence > settings.SCENE_CLASSIFIER_CONFIDENCE
        )
        
        return high_confidence_classifications / total_classifications
    
    def _calculate_detection_rate(self) -> float:
        """Calculate object detection rate."""
        wagon_frames = sum(1 for result in self.detection_history if result.scene_type == 'wagon')
        if wagon_frames == 0:
            return 0.0
        
        frames_with_detections = sum(
            1 for result in self.detection_history 
            if result.scene_type == 'wagon' and (result.wagons or result.numbers)
        )
        
        return frames_with_detections / wagon_frames
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Get current performance metrics."""
        if self.total_frames_processed == 0:
            return {}
        
        return {
            'frames_processed': self.total_frames_processed,
            'total_processing_time': self.total_processing_time,
            'average_fps': self.total_frames_processed / max(0.001, self.total_processing_time),
            'average_frame_time': self.total_processing_time / self.total_frames_processed
        }
