"""
Configuration management for the railway wagon recognition system.
"""

import os
from pathlib import Path
from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application
    APP_NAME: str = "Railway Wagon Recognition System"
    APP_VERSION: str = "2.0.0"
    DEBUG: bool = False
    
    # API
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # Database
    DATABASE_URL: str = "sqlite:///./wagon_recognition.db"
    
    # AI Models
    MODELS_DIR: Path = Path("models")
    SCENE_CLASSIFIER_MODEL: str = "scene_classifier.pt"
    YOLO_MODEL: str = "yolo_wagon.pt"
    
    # Processing
    MAX_CONCURRENT_JOBS: int = 4
    MAX_FILE_SIZE_MB: int = 500
    SUPPORTED_VIDEO_FORMATS: List[str] = [".mp4", ".avi", ".mov", ".mkv"]
    
    # Performance
    TARGET_FPS: int = 10
    MAX_RESOLUTION_WIDTH: int = 1920
    MAX_RESOLUTION_HEIGHT: int = 1080
    
    # AI Parameters
    SCENE_CLASSIFIER_CONFIDENCE: float = 0.8
    YOLO_CONFIDENCE: float = 0.5
    OCR_CONFIDENCE: float = 0.7
    
    # Wagon Counting
    MIN_WAGON_FRAMES: int = 5  # Minimum consecutive frames to count as wagon
    MIN_GAP_FRAMES: int = 3    # Minimum consecutive frames to count as gap
    
    # Number Recognition
    MIN_NUMBER_LENGTH: int = 6
    MAX_NUMBER_LENGTH: int = 8
    ENABLE_CHECKSUM_VALIDATION: bool = True
    
    # File Storage
    UPLOAD_DIR: Path = Path("uploads")
    RESULTS_DIR: Path = Path("results")
    TEMP_DIR: Path = Path("temp")
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 8001
    
    # CORS
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000"]
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()

# Create necessary directories
for directory in [settings.UPLOAD_DIR, settings.RESULTS_DIR, settings.TEMP_DIR, settings.MODELS_DIR]:
    directory.mkdir(exist_ok=True)
