#!/usr/bin/env python3
"""
Wagons Recognition - Python Implementation

A Python port of the original C++ WagonsRecognition project for railway wagon 
number detection using computer vision.

Usage:
    python main.py

Controls:
    ESC - Exit application
    SPACE - Pause/resume video playback
    Trackbar - Navigate through video frames (video mode only)
"""

import sys
import logging
import os
import argparse
from pathlib import Path
from typing import Optional
import cv2
import numpy as np

# Add project root to path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from detection import MorphologyDetector
from utils import concat_images, VideoManager, Direction
from config import WINDOW_NAME, TRACKBAR_NAME, WITH_TRACKBAR

# Try to import new AI pipeline
try:
    from ai.pipeline import WagonRecognitionPipeline
    AI_PIPELINE_AVAILABLE = True
except ImportError:
    AI_PIPELINE_AVAILABLE = False
    print("New AI pipeline not available, using legacy detection")


class WagonsRecognitionApp:
    """Main application class for wagon recognition."""

    def __init__(self):
        self.video_manager = VideoManager()
        self.detector = MorphologyDetector()
        self.ai_pipeline = None
        self.use_ai_pipeline = False
        self.slider_position = 0
        self.paused = False
        self.logger = self._setup_logging()

        # Try to initialize AI pipeline
        if AI_PIPELINE_AVAILABLE:
            self.ai_pipeline = WagonRecognitionPipeline()
            if self.ai_pipeline.initialize():
                self.use_ai_pipeline = True
                self.logger.info("AI pipeline initialized successfully")
            else:
                self.logger.warning("AI pipeline initialization failed, using legacy detection")

    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration."""
        logger = logging.getLogger('WagonsRecognition')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger
        
    def on_trackbar_change(self, position: int) -> None:
        """
        Callback for trackbar position changes.
        
        Args:
            position: New trackbar position
        """
        if WITH_TRACKBAR and not self.video_manager.is_camera:
            self.slider_position = position
            self.video_manager.set_frame_position(position)
    
    def choose_input_source(self) -> bool:
        """
        Let user choose between video file and camera input.

        Returns:
            True if source was successfully opened, False otherwise
        """
        print("Wagons Recognition - Python Implementation")
        print("=" * 45)
        print("\nChoose input source:")
        print("1 - Video file")
        print("2 - Camera")

        while True:
            try:
                choice = input("\nEnter your choice (1 or 2): ").strip()

                if choice == "1":
                    # Video file mode
                    self.logger.info("User selected video file mode")
                    video_path = self.video_manager.choose_video_file()
                    if video_path is None:
                        self.logger.info("Video file selection cancelled")
                        return False

                    if self.video_manager.open_video_file(video_path):
                        print(f"Opened video: {video_path.name}")
                        self.logger.info(f"Successfully opened video: {video_path}")
                        return True
                    else:
                        print(f"Failed to open video: {video_path}")
                        self.logger.error(f"Failed to open video: {video_path}")
                        return False

                elif choice == "2":
                    # Camera mode
                    self.logger.info("User selected camera mode")
                    if self.video_manager.open_camera():
                        print("Camera opened successfully")
                        self.logger.info("Camera opened successfully")
                        return True
                    else:
                        print("Failed to open camera")
                        self.logger.error("Failed to open camera")
                        return False
                else:
                    print("Please enter 1 or 2")

            except KeyboardInterrupt:
                print("\nExiting...")
                self.logger.info("Application interrupted by user")
                return False
            except Exception as e:
                self.logger.error(f"Unexpected error in input source selection: {e}")
                print(f"An error occurred: {e}")
                return False
    
    def setup_window(self) -> None:
        """Setup the main window and trackbar if needed."""
        cv2.namedWindow(WINDOW_NAME, cv2.WINDOW_AUTOSIZE)
        
        # Create trackbar for video files only
        if not self.video_manager.is_camera and WITH_TRACKBAR:
            frame_count = self.video_manager.get_frame_count()
            cv2.createTrackbar(
                TRACKBAR_NAME, 
                WINDOW_NAME, 
                0, 
                frame_count - 1, 
                self.on_trackbar_change
            )
    
    def process_frame(self, frame: np.ndarray) -> np.ndarray:
        """
        Process a single frame for wagon detection.

        Args:
            frame: Input frame

        Returns:
            Processed frame with detections drawn
        """
        if self.use_ai_pipeline and self.ai_pipeline:
            # Use new AI pipeline
            return self._process_frame_ai(frame)
        else:
            # Use legacy morphological detection
            return self._process_frame_legacy(frame)

    def _process_frame_ai(self, frame: np.ndarray) -> np.ndarray:
        """Process frame using AI pipeline."""
        # Process frame through AI pipeline
        result = self.ai_pipeline.process_frame(frame, self.slider_position)

        # Create visualization
        result_frame = frame.copy()

        # Draw wagon detections
        for wagon in result.wagons:
            bbox = wagon['bbox']
            confidence = wagon['confidence']
            x1, y1, x2, y2 = bbox
            cv2.rectangle(result_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.putText(result_frame, f"Wagon: {confidence:.2f}", (x1, y1-5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # Draw number detections
        for number in result.numbers:
            bbox = number['bbox']
            text = number['text']
            confidence = number['confidence']
            x1, y1, x2, y2 = bbox
            cv2.rectangle(result_frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
            cv2.putText(result_frame, f"{text}: {confidence:.2f}", (x1, y1-5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

        # Add scene classification info
        cv2.putText(result_frame, f"Scene: {result.scene_type} ({result.scene_confidence:.2f})",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        return result_frame

    def _process_frame_legacy(self, frame: np.ndarray) -> np.ndarray:
        """Process frame using legacy morphological detection."""
        # Clear previous detections
        self.detector.clear_rects()

        # Detect wagon numbers
        self.detector.detect(frame)

        # Draw detection rectangles
        result_frame = frame.copy()
        self.detector.draw_rects(result_frame)

        # Get processed image for debugging
        processed = self.detector.get_processed_image(frame)

        # Concatenate original and processed images
        combined = concat_images(result_frame, processed, Direction.HORIZONTAL)

        return combined
    
    def run(self, input_source: Optional[str] = None, video_path: Optional[str] = None) -> None:
        """Main application loop.

        Args:
            input_source: Optional input source ('video' or 'camera') for non-interactive mode
            video_path: Optional path to video file for 'video' mode
        """
        # Choose input source (interactive by default)
        if input_source is None:
            if not self.choose_input_source():
                return
        else:
            # Non-interactive mode
            source = input_source.lower()
            if source == 'video':
                if video_path is None:
                    # Try environment variable
                    video_path = os.environ.get('VIDEO_PATH')
                if not video_path:
                    print("VIDEO_PATH is not provided for video mode.")
                    return
                if self.video_manager.open_video_file(video_path):
                    print(f"Opened video: {Path(video_path).name}")
                else:
                    print(f"Failed to open video: {video_path}")
                    return
            elif source == 'camera':
                if self.video_manager.open_camera():
                    print("Camera opened successfully")
                else:
                    print("Failed to open camera")
                    return
            else:
                print("Invalid input source. Use 'video' or 'camera'.")
                return
        
        # Setup window
        self.setup_window()
        
        # Get video properties
        fps = self.video_manager.get_fps()
        delay = max(1, int(1000 / fps))
        
        print(f"\nVideo properties:")
        print(f"FPS: {fps:.2f}")
        print(f"Frame delay: {delay}ms")
        
        if not self.video_manager.is_camera:
            frame_count = self.video_manager.get_frame_count()
            print(f"Total frames: {frame_count}")
        
        print(f"\nControls:")
        print(f"ESC - Exit")
        print(f"SPACE - Pause/Resume")
        if not self.video_manager.is_camera:
            print(f"Trackbar - Navigate frames")
        
        # Main processing loop
        try:
            while True:
                # Read frame
                success, frame = self.video_manager.read_frame()
                
                if not success:
                    if self.video_manager.is_camera:
                        print("Failed to read from camera")
                        break
                    else:
                        print("End of video reached")
                        break
                
                # Update trackbar position for video files
                if not self.video_manager.is_camera and WITH_TRACKBAR:
                    self.slider_position += 1
                    cv2.setTrackbarPos(TRACKBAR_NAME, WINDOW_NAME, self.slider_position)
                
                # Process frame
                processed_frame = self.process_frame(frame)
                
                # Display result
                cv2.imshow(WINDOW_NAME, processed_frame)
                
                # Handle keyboard input
                key = cv2.waitKey(delay if not self.paused else 0) & 0xFF
                
                if key == 27:  # ESC key
                    break
                elif key == 32:  # SPACE key
                    self.paused = not self.paused
                    if self.paused:
                        print("Paused - Press SPACE to resume")
                    else:
                        print("Resumed")
                        
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        
        finally:
            # Cleanup
            self.cleanup()
    
    def cleanup(self) -> None:
        """Clean up resources."""
        self.video_manager.release()
        cv2.destroyAllWindows()
        print("Cleanup completed")


def main():
    """Main entry point."""
    # Parse CLI arguments (for non-interactive mode in containers)
    parser = argparse.ArgumentParser(description="Wagons Recognition")
    parser.add_argument("--input", dest="input_source", choices=["video", "camera"], help="Input source: video or camera")
    parser.add_argument("--video", dest="video_path", help="Path to video file (when --input=video)")
    args = parser.parse_args()

    # Allow environment variables as fallback
    input_source = args.input_source or os.environ.get("INPUT_SOURCE")
    video_path = args.video_path or os.environ.get("VIDEO_PATH")

    app = WagonsRecognitionApp()
    app.run(input_source=input_source, video_path=video_path)


if __name__ == "__main__":
    main()
