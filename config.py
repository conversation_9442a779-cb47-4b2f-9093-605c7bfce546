"""
Configuration settings for the Wagons Recognition application.
"""

from pathlib import Path
from typing import List

# Display settings
WINDOW_NAME = "Wagons Recognition - Python"
TRACKBAR_NAME = "Frame No"
WITH_TRACKBAR = True

# Recognition settings
CHAR_HEIGHT = 47

# Detection parameters (adjust these based on your video content)
BLUR_SIZE = 5  # Blur kernel size for noise reduction
MIN_CONTOUR_AREA = 100  # Minimum area for valid detections
MAX_CONTOUR_AREA = 10000  # Maximum area for valid detections
MIN_ASPECT_RATIO = 1.5  # Minimum width/height ratio
MAX_ASPECT_RATIO = 8.0  # Maximum width/height ratio

# Colors (BGR format for OpenCV)
COLOR_RED = (0, 0, 255)
COLOR_GREEN = (0, 255, 0)

# Video settings
DEFAULT_VIDEO_PATH = Path("videos")  # Relative to project root
VIDEO_EXTENSIONS = [".avi", ".mp4", ".mov", ".mkv"]

# Sample video files (can be customized)
SAMPLE_VIDEO_FILES = [
    "sample1.avi",
    "sample2.avi",
    "wagons.avi",
    "cisterns.avi",
    "hoppers.avi"
]

# Camera settings
DEFAULT_CAMERA_INDEX = 0

# Processing settings
def get_blur_size(char_height: int = CHAR_HEIGHT) -> int:
    """Calculate blur kernel size based on character height."""
    return max(1, BLUR_SIZE)

# Morphological operation settings
MORPH_KERNEL_SIZE = (17, 3)
MORPH_OPERATION = "close"  # close, open, gradient, etc.

# Debug settings
DEBUG_MODE = False  # Set to True to enable debug output
SAVE_DEBUG_FRAMES = False  # Set to True to save intermediate processing frames
