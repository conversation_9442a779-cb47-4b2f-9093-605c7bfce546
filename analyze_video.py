#!/usr/bin/env python3
"""
Video analysis tool to understand content and suggest detection parameters.
"""

import sys
import cv2
import numpy as np
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils import VideoManager


def analyze_frame_content(frame):
    """Analyze frame content to suggest detection parameters."""
    # Convert to grayscale
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    # Calculate basic statistics
    mean_brightness = np.mean(gray)
    std_brightness = np.std(gray)
    
    # Edge analysis
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.sum(edges > 0) / edges.size
    
    # Contour analysis with different parameters
    contour_counts = {}
    for threshold in [50, 100, 150, 200]:
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        contour_counts[threshold] = len(contours)
    
    # Text-like region detection (high frequency content)
    sobel_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    sobel_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
    high_freq_density = np.sum(sobel_magnitude > 50) / sobel_magnitude.size
    
    return {
        'mean_brightness': mean_brightness,
        'std_brightness': std_brightness,
        'edge_density': edge_density,
        'contour_counts': contour_counts,
        'high_freq_density': high_freq_density,
        'frame_size': frame.shape[:2]
    }


def suggest_parameters(analysis):
    """Suggest detection parameters based on frame analysis."""
    suggestions = {}
    
    # Character height based on frame size
    height, width = analysis['frame_size']
    suggested_char_height = max(20, min(100, height // 15))
    suggestions['char_height'] = suggested_char_height
    
    # Blur size based on noise level
    if analysis['std_brightness'] > 50:
        suggestions['blur_size'] = 7  # More blur for noisy images
    elif analysis['std_brightness'] > 30:
        suggestions['blur_size'] = 5
    else:
        suggestions['blur_size'] = 3  # Less blur for clean images
    
    # Morphology kernel based on character height
    suggestions['morph_width'] = max(10, suggested_char_height // 3)
    suggestions['morph_height'] = max(2, suggested_char_height // 15)
    
    # Area thresholds based on character height
    char_area = suggested_char_height * (suggested_char_height // 2)
    suggestions['min_area'] = char_area // 4
    suggestions['max_area'] = char_area * 20
    
    # Aspect ratio for typical wagon numbers
    suggestions['min_aspect_ratio'] = 1.5
    suggestions['max_aspect_ratio'] = 8.0
    
    return suggestions


def create_analysis_display(frame, analysis, suggestions):
    """Create a display showing analysis results."""
    display = frame.copy()
    height, width = display.shape[:2]
    
    # Create info panel
    info_panel = np.zeros((height, 400, 3), dtype=np.uint8)
    
    # Add analysis text
    texts = [
        "FRAME ANALYSIS",
        "=" * 20,
        f"Size: {width}x{height}",
        f"Brightness: {analysis['mean_brightness']:.1f}±{analysis['std_brightness']:.1f}",
        f"Edge density: {analysis['edge_density']:.3f}",
        f"High freq: {analysis['high_freq_density']:.3f}",
        "",
        "CONTOURS BY THRESHOLD:",
        f"  50: {analysis['contour_counts'][50]}",
        f" 100: {analysis['contour_counts'][100]}",
        f" 150: {analysis['contour_counts'][150]}",
        f" 200: {analysis['contour_counts'][200]}",
        "",
        "SUGGESTED PARAMETERS:",
        "=" * 20,
        f"Char Height: {suggestions['char_height']}",
        f"Blur Size: {suggestions['blur_size']}",
        f"Morph: {suggestions['morph_width']}x{suggestions['morph_height']}",
        f"Min Area: {suggestions['min_area']}",
        f"Max Area: {suggestions['max_area']}",
        f"Aspect: {suggestions['min_aspect_ratio']:.1f}-{suggestions['max_aspect_ratio']:.1f}",
        "",
        "CONTROLS:",
        "SPACE - Next frame",
        "S - Save analysis",
        "ESC - Exit"
    ]
    
    y_pos = 30
    for text in texts:
        if text.startswith("="):
            color = (0, 255, 255)  # Yellow for separators
        elif text.startswith("SUGGESTED") or text.startswith("FRAME") or text.startswith("CONTROLS"):
            color = (0, 255, 0)  # Green for headers
        else:
            color = (255, 255, 255)  # White for regular text
        
        cv2.putText(info_panel, text, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        y_pos += 20
    
    # Combine frame and info panel
    combined = np.hstack([display, info_panel])
    return combined


def main():
    """Main analysis application."""
    print("Video Content Analysis Tool")
    print("=" * 30)
    
    # Setup video manager
    video_manager = VideoManager()
    
    # Choose video file
    video_path = video_manager.choose_video_file()
    if not video_path or not video_manager.open_video_file(video_path):
        print("Failed to open video file")
        return
    
    print(f"Opened: {video_path.name}")
    print(f"Total frames: {video_manager.get_frame_count()}")
    print("\nAnalyzing video content...")
    print("Use SPACE to advance frames, S to save analysis, ESC to exit")
    
    frame_count = 0
    analyses = []
    
    try:
        while True:
            success, frame = video_manager.read_frame()
            if not success:
                print("End of video reached")
                break
            
            frame_count += 1
            
            # Analyze frame
            analysis = analyze_frame_content(frame)
            suggestions = suggest_parameters(analysis)
            analyses.append((frame_count, analysis, suggestions))
            
            # Create display
            display = create_analysis_display(frame, analysis, suggestions)
            
            # Add frame counter
            cv2.putText(display, f"Frame: {frame_count}/{video_manager.get_frame_count()}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
            
            cv2.imshow("Video Analysis", display)
            
            # Handle keyboard input
            key = cv2.waitKey(0) & 0xFF  # Wait for key press
            
            if key == 27:  # ESC
                break
            elif key == ord('s'):  # Save analysis
                save_name = f"analysis_frame_{frame_count:04d}.jpg"
                cv2.imwrite(save_name, display)
                print(f"Saved: {save_name}")
            # SPACE or any other key continues to next frame
    
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    
    finally:
        video_manager.release()
        cv2.destroyAllWindows()
        
        # Print summary
        if analyses:
            print(f"\nAnalyzed {len(analyses)} frames")
            print("\nAverage suggested parameters:")
            
            avg_char_height = np.mean([s[2]['char_height'] for s in analyses])
            avg_blur_size = np.mean([s[2]['blur_size'] for s in analyses])
            avg_morph_width = np.mean([s[2]['morph_width'] for s in analyses])
            avg_morph_height = np.mean([s[2]['morph_height'] for s in analyses])
            avg_min_area = np.mean([s[2]['min_area'] for s in analyses])
            avg_max_area = np.mean([s[2]['max_area'] for s in analyses])
            
            print(f"Character Height: {avg_char_height:.0f}")
            print(f"Blur Size: {avg_blur_size:.0f}")
            print(f"Morphology Kernel: {avg_morph_width:.0f}x{avg_morph_height:.0f}")
            print(f"Area Range: {avg_min_area:.0f} - {avg_max_area:.0f}")
            print(f"Aspect Ratio: 1.5 - 8.0")
            
            print(f"\nTo use these parameters, update config.py:")
            print(f"CHAR_HEIGHT = {avg_char_height:.0f}")
            print(f"MORPH_KERNEL_SIZE = ({avg_morph_width:.0f}, {avg_morph_height:.0f})")


if __name__ == "__main__":
    main()
