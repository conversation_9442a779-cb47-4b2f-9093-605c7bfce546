# Wagons Recognition - Docker Image
# Multi-stage build for optimized image size

# Build stage
FROM python:3.11-slim as builder

# Install system dependencies for OpenCV
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    pkg-config \
    libgtk-3-dev \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    gfortran \
    openexr \
    libatlas-base-dev \
    python3-dev \
    python3-numpy \
    libtbb2 \
    libtbb-dev \
    libdc1394-22-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.11-slim

# Install runtime dependencies for OpenCV and GUI
RUN apt-get update && apt-get install -y \
    libgtk-3-0 \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgstreamer1.0-0 \
    libgstreamer-plugins-base1.0-0 \
    libavcodec58 \
    libavformat58 \
    libswscale5 \
    libjpeg62-turbo \
    libpng16-16 \
    libtiff5 \
    libopenexr24 \
    libatlas3-base \
    libtbb2 \
    libdc1394-22 \
    # For camera access
    v4l-utils \
    # For X11 forwarding (GUI support)
    x11-apps \
    && rm -rf /var/lib/apt/lists/*

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Create non-root user for security
RUN groupadd -r wagons && useradd -r -g wagons -u 1000 wagons

# Set working directory
WORKDIR /app

# Copy application code
COPY --chown=wagons:wagons . .

# Create videos directory with proper permissions
RUN mkdir -p /app/videos && chown -R wagons:wagons /app/videos

# Switch to non-root user
USER wagons

# Set environment variables
ENV PYTHONPATH=/app
ENV DISPLAY=:0

# Expose any ports if needed (for future web interface)
# EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import cv2, numpy; print('OK')" || exit 1

# Default command
CMD ["python", "main.py"]
