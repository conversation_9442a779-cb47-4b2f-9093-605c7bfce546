# Usage Guide

## Quick Start

1. **Install the application** (see INSTALL.md)
2. **Run the application**:
   ```bash
   python main.py
   ```
3. **Choose input source**:
   - Enter `1` for video file
   - Enter `2` for camera

## Detailed Usage

### Video File Mode

1. Select option `1` when prompted
2. Choose from available video files in the `videos/` directory
3. Use the trackbar to navigate through frames
4. Press `SPACE` to pause/resume playback
5. Press `ESC` to exit

### Camera Mode

1. Select option `2` when prompted
2. The application will open your default camera
3. Point the camera at railway wagons
4. Press `SPACE` to pause/resume capture
5. Press `ESC` to exit

## Controls

| Key | Action |
|-----|--------|
| `ESC` | Exit application |
| `SPACE` | Pause/Resume video |
| Trackbar | Navigate video frames (video mode only) |

## Understanding the Output

The application displays two side-by-side images:

1. **Left side**: Original video with detected wagon number regions highlighted in red rectangles
2. **Right side**: Processed binary image showing the morphological detection results

### Detection Rectangles

- **Red rectangles**: Detected potential wagon number regions
- The algorithm looks for areas with high density of vertical lines (typical of text/numbers)

## Configuration

You can modify detection parameters in `config.py`:

```python
# Recognition settings
CHAR_HEIGHT = 47  # Expected character height in pixels

# Morphological operation settings  
MORPH_KERNEL_SIZE = (17, 3)  # Kernel size for morphological operations

# Display settings
WITH_TRACKBAR = True  # Enable/disable trackbar for video files
```

## Tips for Better Detection

### Video Quality
- Use high-resolution videos when possible
- Ensure good lighting conditions
- Avoid motion blur

### Camera Setup
- Position camera to capture wagon sides clearly
- Maintain stable camera position
- Ensure adequate lighting

### Wagon Numbers
- Works best with standard ex-USSR railway wagon numbering
- Numbers should be clearly visible and not obscured
- Works with various wagon types (cisterns, hoppers, etc.)

## Troubleshooting

### No Detections Appearing
- Check if wagon numbers are clearly visible in the video/camera
- Adjust `CHAR_HEIGHT` in config.py to match your video scale
- Ensure adequate contrast between numbers and wagon surface

### Too Many False Detections
- Increase the minimum size requirements in the `verify_size()` method
- Adjust morphological kernel size in config.py

### Performance Issues
- Reduce video resolution
- Close other applications
- Use video files instead of camera for consistent performance

## Example Workflow

1. **Prepare video files**:
   ```bash
   # Copy your wagon videos to the videos directory
   cp /path/to/your/wagon_video.avi videos/
   ```

2. **Run detection**:
   ```bash
   python main.py
   # Select option 1 (video file)
   # Choose your video from the list
   ```

3. **Analyze results**:
   - Watch the red rectangles appear around detected number regions
   - Use the trackbar to review specific frames
   - Note the processed image on the right showing detection areas

4. **Adjust parameters** (if needed):
   - Edit `config.py` to fine-tune detection
   - Restart the application to apply changes
