"""
Object Tracker for Railway Wagon Recognition

Implements multi-object tracking to count unique wagons and maintain
consistent identification across video frames.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

import numpy as np

from app.core.config import settings


@dataclass
class WagonTrack:
    """Represents a tracked wagon across multiple frames."""
    track_id: int
    first_frame: int
    last_frame: int
    bboxes: List[Tuple[int, int, int, int]]  # List of bounding boxes
    confidences: List[float]
    recognized_numbers: List[str]
    number_confidences: List[float]
    is_active: bool = True


class WagonTracker:
    """
    Multi-object tracker for railway wagons.
    
    Tracks wagons across video frames to:
    - Count unique wagons accurately
    - Consolidate number recognition from multiple frames
    - Handle occlusions and temporary detection failures
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.tracks: Dict[int, WagonTrack] = {}
        self.next_track_id = 1
        self.frame_state_history = []
        
        # Tracking parameters
        self.max_distance_threshold = 100  # Maximum pixel distance for track association
        self.max_frames_missing = 10       # Maximum frames a track can be missing
        self.min_track_length = 5          # Minimum frames for a valid track
    
    def count_wagons(self, detection_history: List[Any]) -> int:
        """
        Count unique wagons based on detection history.
        
        Args:
            detection_history: List of DetectionResult objects
            
        Returns:
            Number of unique wagons counted
        """
        self.logger.info("Starting wagon counting from detection history")
        
        # Reset tracking state
        self._reset_tracking()
        
        # Process each frame
        for detection_result in detection_history:
            self._process_frame(detection_result)
        
        # Finalize tracking
        self._finalize_tracks()
        
        # Count valid tracks
        valid_tracks = [track for track in self.tracks.values() 
                       if self._is_valid_track(track)]
        
        wagon_count = len(valid_tracks)
        
        self.logger.info(f"Counted {wagon_count} unique wagons from {len(self.tracks)} total tracks")
        
        return wagon_count
    
    def get_consolidated_numbers(self) -> List[Dict[str, Any]]:
        """
        Get consolidated number recognition results.
        
        Returns:
            List of consolidated numbers with confidence scores
        """
        consolidated_numbers = []
        
        for track in self.tracks.values():
            if not self._is_valid_track(track) or not track.recognized_numbers:
                continue
            
            # Find the best recognized number for this track
            best_number = self._get_best_number_for_track(track)
            if best_number:
                consolidated_numbers.append(best_number)
        
        # Sort by confidence
        consolidated_numbers.sort(key=lambda x: x['confidence'], reverse=True)
        
        return consolidated_numbers
    
    def _reset_tracking(self):
        """Reset tracking state for new processing."""
        self.tracks.clear()
        self.next_track_id = 1
        self.frame_state_history.clear()
    
    def _process_frame(self, detection_result):
        """
        Process detections from a single frame.
        
        Args:
            detection_result: DetectionResult object
        """
        frame_number = detection_result.frame_number
        
        # Extract wagon detections
        wagon_detections = [d for d in detection_result.wagons if d.get('class') == 'wagon']
        number_detections = [d for d in detection_result.numbers if d.get('validated', False)]
        
        # Update existing tracks
        self._update_tracks(frame_number, wagon_detections, number_detections)
        
        # Create new tracks for unmatched detections
        self._create_new_tracks(frame_number, wagon_detections, number_detections)
        
        # Mark inactive tracks
        self._mark_inactive_tracks(frame_number)
    
    def _update_tracks(self, frame_number: int, wagon_detections: List[Dict], number_detections: List[Dict]):
        """Update existing tracks with new detections."""
        matched_detections = set()
        
        for track_id, track in self.tracks.items():
            if not track.is_active:
                continue
            
            # Find best matching detection
            best_match = None
            best_distance = float('inf')
            
            for i, detection in enumerate(wagon_detections):
                if i in matched_detections:
                    continue
                
                distance = self._calculate_distance(track.bboxes[-1], detection['bbox'])
                if distance < best_distance and distance < self.max_distance_threshold:
                    best_distance = distance
                    best_match = i
            
            if best_match is not None:
                # Update track with matched detection
                detection = wagon_detections[best_match]
                track.last_frame = frame_number
                track.bboxes.append(detection['bbox'])
                track.confidences.append(detection['confidence'])
                
                # Associate number detections within this wagon's bbox
                wagon_bbox = detection['bbox']
                for number_detection in number_detections:
                    if self._bbox_overlap(wagon_bbox, number_detection['bbox']) > 0.3:
                        track.recognized_numbers.append(number_detection['text'])
                        track.number_confidences.append(number_detection['confidence'])
                
                matched_detections.add(best_match)
    
    def _create_new_tracks(self, frame_number: int, wagon_detections: List[Dict], number_detections: List[Dict]):
        """Create new tracks for unmatched detections."""
        matched_detections = set()
        
        # Find which detections were already matched
        for track in self.tracks.values():
            if track.last_frame == frame_number and track.is_active:
                # This track was updated in current frame
                for i, detection in enumerate(wagon_detections):
                    if self._calculate_distance(track.bboxes[-1], detection['bbox']) < self.max_distance_threshold:
                        matched_detections.add(i)
                        break
        
        # Create new tracks for unmatched detections
        for i, detection in enumerate(wagon_detections):
            if i not in matched_detections:
                track = WagonTrack(
                    track_id=self.next_track_id,
                    first_frame=frame_number,
                    last_frame=frame_number,
                    bboxes=[detection['bbox']],
                    confidences=[detection['confidence']],
                    recognized_numbers=[],
                    number_confidences=[]
                )
                
                # Associate number detections
                wagon_bbox = detection['bbox']
                for number_detection in number_detections:
                    if self._bbox_overlap(wagon_bbox, number_detection['bbox']) > 0.3:
                        track.recognized_numbers.append(number_detection['text'])
                        track.number_confidences.append(number_detection['confidence'])
                
                self.tracks[self.next_track_id] = track
                self.next_track_id += 1
    
    def _mark_inactive_tracks(self, frame_number: int):
        """Mark tracks as inactive if they haven't been updated recently."""
        for track in self.tracks.values():
            if track.is_active and (frame_number - track.last_frame) > self.max_frames_missing:
                track.is_active = False
    
    def _finalize_tracks(self):
        """Finalize all tracks after processing is complete."""
        for track in self.tracks.values():
            track.is_active = False
    
    def _is_valid_track(self, track: WagonTrack) -> bool:
        """
        Check if a track represents a valid wagon.
        
        Args:
            track: WagonTrack to validate
            
        Returns:
            True if track is valid
        """
        # Check minimum track length
        track_length = track.last_frame - track.first_frame + 1
        if track_length < self.min_track_length:
            return False
        
        # Check if track has enough detections
        if len(track.bboxes) < self.min_track_length:
            return False
        
        # Check average confidence
        avg_confidence = np.mean(track.confidences) if track.confidences else 0
        if avg_confidence < 0.3:
            return False
        
        return True
    
    def _get_best_number_for_track(self, track: WagonTrack) -> Optional[Dict[str, Any]]:
        """
        Get the best recognized number for a track.
        
        Args:
            track: WagonTrack to get number for
            
        Returns:
            Best number recognition result or None
        """
        if not track.recognized_numbers:
            return None
        
        # Group similar numbers
        number_groups = defaultdict(list)
        for number, confidence in zip(track.recognized_numbers, track.number_confidences):
            number_groups[number].append(confidence)
        
        # Find the number with highest average confidence
        best_number = None
        best_confidence = 0
        
        for number, confidences in number_groups.items():
            avg_confidence = np.mean(confidences)
            if avg_confidence > best_confidence:
                best_confidence = avg_confidence
                best_number = number
        
        if best_number:
            return {
                'text': best_number,
                'confidence': best_confidence,
                'track_id': track.track_id,
                'recognition_count': len(number_groups[best_number])
            }
        
        return None
    
    def _calculate_distance(self, bbox1: Tuple[int, int, int, int], bbox2: Tuple[int, int, int, int]) -> float:
        """
        Calculate distance between two bounding boxes.
        
        Args:
            bbox1: First bounding box (x1, y1, x2, y2)
            bbox2: Second bounding box (x1, y1, x2, y2)
            
        Returns:
            Distance between box centers
        """
        # Calculate centers
        center1 = ((bbox1[0] + bbox1[2]) / 2, (bbox1[1] + bbox1[3]) / 2)
        center2 = ((bbox2[0] + bbox2[2]) / 2, (bbox2[1] + bbox2[3]) / 2)
        
        # Euclidean distance
        distance = np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
        
        return distance
    
    def _bbox_overlap(self, bbox1: Tuple[int, int, int, int], bbox2: Tuple[int, int, int, int]) -> float:
        """
        Calculate overlap ratio between two bounding boxes.
        
        Args:
            bbox1: First bounding box (x1, y1, x2, y2)
            bbox2: Second bounding box (x1, y1, x2, y2)
            
        Returns:
            Overlap ratio (0.0 to 1.0)
        """
        # Calculate intersection
        x1 = max(bbox1[0], bbox2[0])
        y1 = max(bbox1[1], bbox2[1])
        x2 = min(bbox1[2], bbox2[2])
        y2 = min(bbox1[3], bbox2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        
        # Calculate areas
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        
        # Calculate union
        union = area1 + area2 - intersection
        
        if union == 0:
            return 0.0
        
        return intersection / union
    
    def get_tracking_stats(self) -> Dict[str, Any]:
        """Get tracking statistics."""
        valid_tracks = [track for track in self.tracks.values() if self._is_valid_track(track)]
        
        return {
            'total_tracks': len(self.tracks),
            'valid_tracks': len(valid_tracks),
            'tracks_with_numbers': len([track for track in valid_tracks if track.recognized_numbers]),
            'average_track_length': np.mean([len(track.bboxes) for track in valid_tracks]) if valid_tracks else 0,
            'tracking_parameters': {
                'max_distance_threshold': self.max_distance_threshold,
                'max_frames_missing': self.max_frames_missing,
                'min_track_length': self.min_track_length
            }
        }
