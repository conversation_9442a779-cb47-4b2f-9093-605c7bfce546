"""
Number Validator for Railway Wagon Recognition

Implements validation logic for railway wagon numbers including:
- Checksum validation for ex-USSR railway standards
- Format validation
- Confidence scoring
"""

import logging
import re
from typing import Dict, Any, Optional, Tuple

from app.core.config import settings


class NumberValidator:
    """
    Validator for railway wagon numbers.
    
    Implements validation rules for ex-USSR (CIS countries) railway wagon numbers
    including checksum validation and format checking.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Validation patterns for different wagon number formats
        self.patterns = {
            'standard_8_digit': re.compile(r'^\d{8}$'),  # 8-digit standard format
            'standard_7_digit': re.compile(r'^\d{7}$'),  # 7-digit format
            'mixed_format': re.compile(r'^[A-Z]{1,2}\d{6,7}$'),  # Letter prefix + digits
        }
    
    def validate(self, number: str) -> bool:
        """
        Validate a wagon number.
        
        Args:
            number: Wagon number string to validate
            
        Returns:
            True if number is valid, False otherwise
        """
        if not number:
            return False
        
        # Clean the number
        cleaned_number = self._clean_number(number)
        
        # Check basic format
        if not self._check_format(cleaned_number):
            return False
        
        # Check checksum if enabled
        if settings.ENABLE_CHECKSUM_VALIDATION:
            return self._validate_checksum(cleaned_number)
        
        return True
    
    def validate_with_details(self, number: str) -> Dict[str, Any]:
        """
        Validate a wagon number with detailed results.
        
        Args:
            number: Wagon number string to validate
            
        Returns:
            Dictionary with validation details
        """
        result = {
            'original_number': number,
            'cleaned_number': '',
            'is_valid': False,
            'format_valid': False,
            'checksum_valid': False,
            'confidence_score': 0.0,
            'validation_errors': []
        }
        
        if not number:
            result['validation_errors'].append('Empty number')
            return result
        
        # Clean the number
        cleaned_number = self._clean_number(number)
        result['cleaned_number'] = cleaned_number
        
        # Check basic format
        format_valid = self._check_format(cleaned_number)
        result['format_valid'] = format_valid
        
        if not format_valid:
            result['validation_errors'].append('Invalid format')
        
        # Check checksum if enabled
        checksum_valid = True
        if settings.ENABLE_CHECKSUM_VALIDATION:
            checksum_valid = self._validate_checksum(cleaned_number)
            result['checksum_valid'] = checksum_valid
            
            if not checksum_valid:
                result['validation_errors'].append('Invalid checksum')
        
        # Overall validation
        result['is_valid'] = format_valid and checksum_valid
        
        # Calculate confidence score
        result['confidence_score'] = self._calculate_confidence(cleaned_number, format_valid, checksum_valid)
        
        return result
    
    def _clean_number(self, number: str) -> str:
        """
        Clean and normalize a wagon number.
        
        Args:
            number: Raw number string
            
        Returns:
            Cleaned number string
        """
        # Remove whitespace and convert to uppercase
        cleaned = number.strip().upper()
        
        # Remove common OCR artifacts
        cleaned = re.sub(r'[^\w]', '', cleaned)
        
        # Common character corrections
        corrections = {
            'O': '0', 'I': '1', 'L': '1', 'S': '5', 'G': '6', 'B': '8', 'Z': '2'
        }
        
        for wrong, correct in corrections.items():
            cleaned = cleaned.replace(wrong, correct)
        
        return cleaned
    
    def _check_format(self, number: str) -> bool:
        """
        Check if number matches expected format patterns.
        
        Args:
            number: Cleaned number string
            
        Returns:
            True if format is valid
        """
        if not number:
            return False
        
        # Check length constraints
        if len(number) < settings.MIN_NUMBER_LENGTH or len(number) > settings.MAX_NUMBER_LENGTH:
            return False
        
        # Check against known patterns
        for pattern_name, pattern in self.patterns.items():
            if pattern.match(number):
                self.logger.debug(f"Number {number} matches pattern: {pattern_name}")
                return True
        
        return False
    
    def _validate_checksum(self, number: str) -> bool:
        """
        Validate checksum for ex-USSR railway wagon numbers.
        
        Args:
            number: Cleaned number string
            
        Returns:
            True if checksum is valid
        """
        # Only validate checksum for 8-digit numbers (standard format)
        if not re.match(r'^\d{8}$', number):
            # For non-standard formats, skip checksum validation
            return True
        
        try:
            # Convert to list of integers
            digits = [int(d) for d in number]
            
            # Calculate checksum using ex-USSR railway standard
            # The last digit is the check digit
            check_digit = digits[-1]
            number_digits = digits[:-1]
            
            # Calculate weighted sum
            weights = [2, 1, 2, 1, 2, 1, 2]  # Standard weights for 7 digits
            weighted_sum = sum(digit * weight for digit, weight in zip(number_digits, weights))
            
            # Calculate check digit
            calculated_check = weighted_sum % 10
            if calculated_check != 0:
                calculated_check = 10 - calculated_check
            
            is_valid = calculated_check == check_digit
            
            if not is_valid:
                self.logger.debug(f"Checksum validation failed for {number}: "
                                f"expected {calculated_check}, got {check_digit}")
            
            return is_valid
            
        except (ValueError, IndexError) as e:
            self.logger.error(f"Error validating checksum for {number}: {e}")
            return False
    
    def _calculate_confidence(self, number: str, format_valid: bool, checksum_valid: bool) -> float:
        """
        Calculate confidence score for a number validation.
        
        Args:
            number: Cleaned number string
            format_valid: Whether format validation passed
            checksum_valid: Whether checksum validation passed
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        confidence = 0.0
        
        # Base confidence from format validation
        if format_valid:
            confidence += 0.6
        
        # Additional confidence from checksum validation
        if checksum_valid:
            confidence += 0.3
        
        # Bonus for standard 8-digit format
        if re.match(r'^\d{8}$', number):
            confidence += 0.1
        
        # Penalty for very short or long numbers
        if len(number) < 6 or len(number) > 8:
            confidence -= 0.2
        
        # Ensure confidence is in valid range
        confidence = max(0.0, min(1.0, confidence))
        
        return confidence
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """
        Get validation statistics and configuration.
        
        Returns:
            Dictionary with validation configuration and stats
        """
        return {
            'checksum_validation_enabled': settings.ENABLE_CHECKSUM_VALIDATION,
            'min_number_length': settings.MIN_NUMBER_LENGTH,
            'max_number_length': settings.MAX_NUMBER_LENGTH,
            'supported_patterns': list(self.patterns.keys()),
            'validation_rules': {
                'format_check': 'Length and pattern validation',
                'checksum_check': 'Ex-USSR railway standard checksum' if settings.ENABLE_CHECKSUM_VALIDATION else 'Disabled',
                'character_correction': 'OCR error correction applied'
            }
        }
    
    def batch_validate(self, numbers: list) -> list:
        """
        Validate multiple numbers in batch.
        
        Args:
            numbers: List of number strings to validate
            
        Returns:
            List of validation results
        """
        results = []
        for number in numbers:
            result = self.validate_with_details(number)
            results.append(result)
        
        return results
    
    def suggest_corrections(self, number: str) -> list:
        """
        Suggest possible corrections for invalid numbers.
        
        Args:
            number: Invalid number string
            
        Returns:
            List of suggested corrections
        """
        suggestions = []
        
        if not number:
            return suggestions
        
        cleaned = self._clean_number(number)
        
        # Try different character substitutions
        common_substitutions = [
            ('0', 'O'), ('1', 'I'), ('1', 'L'), ('5', 'S'), 
            ('6', 'G'), ('8', 'B'), ('2', 'Z')
        ]
        
        for original, replacement in common_substitutions:
            if original in cleaned:
                candidate = cleaned.replace(original, replacement)
                if self.validate(candidate) and candidate != cleaned:
                    suggestions.append({
                        'suggestion': candidate,
                        'change': f"Replace '{original}' with '{replacement}'",
                        'confidence': 0.8
                    })
        
        # Try removing/adding digits for length issues
        if len(cleaned) == settings.MIN_NUMBER_LENGTH - 1:
            # Try adding a digit
            for digit in '0123456789':
                for pos in range(len(cleaned) + 1):
                    candidate = cleaned[:pos] + digit + cleaned[pos:]
                    if self.validate(candidate):
                        suggestions.append({
                            'suggestion': candidate,
                            'change': f"Add '{digit}' at position {pos}",
                            'confidence': 0.6
                        })
        
        elif len(cleaned) == settings.MAX_NUMBER_LENGTH + 1:
            # Try removing a digit
            for pos in range(len(cleaned)):
                candidate = cleaned[:pos] + cleaned[pos+1:]
                if self.validate(candidate):
                    suggestions.append({
                        'suggestion': candidate,
                        'change': f"Remove digit at position {pos}",
                        'confidence': 0.6
                    })
        
        # Sort by confidence and remove duplicates
        unique_suggestions = {}
        for suggestion in suggestions:
            key = suggestion['suggestion']
            if key not in unique_suggestions or suggestion['confidence'] > unique_suggestions[key]['confidence']:
                unique_suggestions[key] = suggestion
        
        return sorted(unique_suggestions.values(), key=lambda x: x['confidence'], reverse=True)[:5]
