@echo off
REM Docker run script for Wagons Recognition (Windows)
REM Automatically sets up X11 forwarding for Windows with VcXsrv

setlocal enabledelayedexpansion

echo [INFO] Wagons Recognition Docker Runner (Windows)
echo ==================================================

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if Docker daemon is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker daemon is not running
    echo [INFO] Please start Docker Desktop
    pause
    exit /b 1
)

echo [INFO] Docker is available

REM Set up X11 forwarding for Windows
echo [INFO] Setting up X11 for Windows
echo [WARNING] Make sure VcXsrv is running with 'Disable access control' checked
set DISPLAY=host.docker.internal:0

REM Create videos directory if it doesn't exist
if not exist "videos" (
    mkdir videos
    echo [INFO] Created videos directory
)

REM Check if image exists, build if not
docker image inspect wagons-recognition:latest >nul 2>&1
if errorlevel 1 (
    echo [INFO] Building Docker image...
    docker build -t wagons-recognition:latest .
    if errorlevel 1 (
        echo [ERROR] Failed to build Docker image
        pause
        exit /b 1
    )
) else (
    echo [INFO] Docker image already exists
)

REM Handle command line arguments
if "%1"=="--build" (
    echo [INFO] Force building image...
    docker build -t wagons-recognition:latest .
    goto :eof
)

if "%1"=="--compose" (
    echo [INFO] Using Docker Compose...
    docker-compose up --build
    goto :eof
)

if "%1"=="--help" (
    echo Usage: %0 [OPTIONS]
    echo Options:
    echo   --build    Force rebuild the Docker image
    echo   --compose  Use Docker Compose instead
    echo   --help     Show this help message
    goto :eof
)

REM Run the Docker container
echo [INFO] Running Docker container...
echo [INFO] Command: docker run -it --rm -e DISPLAY=%DISPLAY% -v "%cd%\videos:/app/videos:rw" wagons-recognition:latest
echo.

docker run -it --rm ^
    -e DISPLAY=%DISPLAY% ^
    -v "%cd%\videos:/app/videos:rw" ^
    wagons-recognition:latest

if errorlevel 1 (
    echo [ERROR] Container execution failed
    echo [INFO] Common issues:
    echo   - VcXsrv not running or not configured properly
    echo   - Docker Desktop not running
    echo   - Insufficient permissions
    pause
)

echo [INFO] Container execution completed
pause
