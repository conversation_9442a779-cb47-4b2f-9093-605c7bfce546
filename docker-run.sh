#!/bin/bash
# Docker run script for Wagons Recognition
# Automatically detects platform and sets appropriate X11 forwarding

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect platform
detect_platform() {
    case "$(uname -s)" in
        Linux*)     PLATFORM=Linux;;
        Darwin*)    PLATFORM=Mac;;
        CYGWIN*|MINGW*|MSYS*) PLATFORM=Windows;;
        *)          PLATFORM="Unknown";;
    esac
    print_info "Detected platform: $PLATFORM"
}

# Check Docker installation
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    print_info "Docker is available"
}

# Setup X11 forwarding based on platform
setup_x11() {
    case $PLATFORM in
        Linux)
            print_info "Setting up X11 for Linux"
            xhost +local:docker 2>/dev/null || print_warning "Could not run xhost +local:docker"
            X11_ARGS="-e DISPLAY=$DISPLAY -v /tmp/.X11-unix:/tmp/.X11-unix:rw"
            ;;
        Mac)
            print_info "Setting up X11 for macOS (requires XQuartz)"
            print_warning "Make sure XQuartz is running and 'Allow connections from network clients' is enabled"
            X11_ARGS="-e DISPLAY=host.docker.internal:0"
            ;;
        Windows)
            print_info "Setting up X11 for Windows (requires VcXsrv)"
            print_warning "Make sure VcXsrv is running with 'Disable access control' checked"
            X11_ARGS="-e DISPLAY=host.docker.internal:0"
            ;;
        *)
            print_error "Unsupported platform: $PLATFORM"
            exit 1
            ;;
    esac
}

# Check for camera devices
check_camera() {
    if [[ $PLATFORM == "Linux" ]]; then
        if ls /dev/video* &> /dev/null; then
            CAMERA_ARGS="--device /dev/video0:/dev/video0"
            print_info "Camera device found: /dev/video0"
        else
            print_warning "No camera devices found"
            CAMERA_ARGS=""
        fi
    else
        print_warning "Camera access not configured for $PLATFORM"
        CAMERA_ARGS=""
    fi
}

# Build image if it doesn't exist
build_image() {
    if ! docker image inspect wagons-recognition:latest &> /dev/null; then
        print_info "Building Docker image..."
        docker build -t wagons-recognition:latest .
    else
        print_info "Docker image already exists"
    fi
}

# Main function
main() {
    print_info "Wagons Recognition Docker Runner"
    echo "=================================="
    
    detect_platform
    check_docker
    setup_x11
    check_camera
    build_image
    
    # Create videos directory if it doesn't exist
    mkdir -p videos
    
    # Construct Docker run command
    DOCKER_CMD="docker run -it --rm \
        $X11_ARGS \
        -v $(pwd)/videos:/app/videos:rw \
        $CAMERA_ARGS \
        wagons-recognition:latest"
    
    print_info "Running Docker container..."
    print_info "Command: $DOCKER_CMD"
    echo
    
    # Execute Docker command
    eval $DOCKER_CMD
}

# Handle script arguments
case "${1:-}" in
    --build)
        print_info "Force building image..."
        docker build -t wagons-recognition:latest .
        ;;
    --compose)
        print_info "Using Docker Compose..."
        docker-compose up --build
        ;;
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo "Options:"
        echo "  --build    Force rebuild the Docker image"
        echo "  --compose  Use Docker Compose instead"
        echo "  --help     Show this help message"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
