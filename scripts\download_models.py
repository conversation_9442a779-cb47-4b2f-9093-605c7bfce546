#!/usr/bin/env python3
"""
Download pre-trained models for the Railway Wagon Recognition System.

This script downloads the necessary AI models:
- Scene classifier (ResNet-18)
- YOLO v8 wagon detector
- Sets up the models directory structure
"""

import sys
import logging
from pathlib import Path
import requests
from tqdm import tqdm

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Model download URLs (these would be real URLs in production)
MODEL_URLS = {
    "scene_classifier.pt": {
        "url": "https://example.com/models/scene_classifier.pt",
        "description": "ResNet-18 scene classifier for wagon/gap/background detection",
        "size_mb": 45
    },
    "yolo_wagon.pt": {
        "url": "https://example.com/models/yolo_wagon.pt", 
        "description": "YOLO v8 model trained for wagon and number detection",
        "size_mb": 12
    }
}

# Fallback: Use pre-trained YOLO models
FALLBACK_MODELS = {
    "yolo_wagon.pt": "yolov8n.pt"  # Use YOLOv8 nano as fallback
}


def download_file(url: str, destination: Path, description: str = "") -> bool:
    """
    Download a file with progress bar.
    
    Args:
        url: URL to download from
        destination: Local file path to save to
        description: Description for progress bar
        
    Returns:
        True if download successful, False otherwise
    """
    try:
        logger.info(f"Downloading {description or destination.name}...")
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(destination, 'wb') as file, tqdm(
            desc=destination.name,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as progress_bar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    file.write(chunk)
                    progress_bar.update(len(chunk))
        
        logger.info(f"Successfully downloaded {destination.name}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to download {destination.name}: {e}")
        if destination.exists():
            destination.unlink()  # Remove partial file
        return False


def setup_yolo_fallback() -> bool:
    """
    Set up YOLO fallback model by downloading YOLOv8 nano.
    
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info("Setting up YOLO fallback model...")
        
        # Import ultralytics to trigger automatic download
        from ultralytics import YOLO
        
        # Download YOLOv8 nano model
        model = YOLO('yolov8n.pt')
        
        # Copy to our models directory with expected name
        import shutil
        yolo_source = Path.home() / '.ultralytics' / 'models' / 'yolov8n.pt'
        yolo_dest = settings.MODELS_DIR / 'yolo_wagon.pt'
        
        if yolo_source.exists():
            shutil.copy2(yolo_source, yolo_dest)
            logger.info(f"YOLO fallback model set up at {yolo_dest}")
            return True
        else:
            logger.warning("Could not find downloaded YOLOv8 model")
            return False
            
    except ImportError:
        logger.warning("Ultralytics not available - cannot set up YOLO fallback")
        return False
    except Exception as e:
        logger.error(f"Failed to set up YOLO fallback: {e}")
        return False


def create_mock_scene_classifier() -> bool:
    """
    Create a mock scene classifier model for development.
    
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info("Creating mock scene classifier model...")
        
        import torch
        import torch.nn as nn
        from torchvision.models import resnet18
        
        # Create ResNet-18 model
        model = resnet18(pretrained=True)
        model.fc = nn.Linear(model.fc.in_features, 3)  # 3 classes: background, gap, wagon
        
        # Save model checkpoint
        checkpoint = {
            'model_state_dict': model.state_dict(),
            'classes': ['background', 'gap', 'wagon'],
            'version': '1.0.0',
            'description': 'Mock scene classifier for development'
        }
        
        model_path = settings.MODELS_DIR / 'scene_classifier.pt'
        torch.save(checkpoint, model_path)
        
        logger.info(f"Mock scene classifier created at {model_path}")
        return True
        
    except ImportError:
        logger.warning("PyTorch not available - cannot create mock scene classifier")
        return False
    except Exception as e:
        logger.error(f"Failed to create mock scene classifier: {e}")
        return False


def main():
    """Main function to download all models."""
    logger.info("Railway Wagon Recognition - Model Download Script")
    logger.info("=" * 50)
    
    # Ensure models directory exists
    settings.MODELS_DIR.mkdir(exist_ok=True)
    logger.info(f"Models directory: {settings.MODELS_DIR}")
    
    success_count = 0
    total_models = len(MODEL_URLS)
    
    # Try to download each model
    for model_name, model_info in MODEL_URLS.items():
        model_path = settings.MODELS_DIR / model_name
        
        if model_path.exists():
            logger.info(f"Model {model_name} already exists, skipping download")
            success_count += 1
            continue
        
        # Try to download the model
        if download_file(model_info["url"], model_path, model_info["description"]):
            success_count += 1
        else:
            logger.warning(f"Failed to download {model_name}, will try fallback options")
    
    # Set up fallback models if downloads failed
    if success_count < total_models:
        logger.info("Setting up fallback models for development...")
        
        # YOLO fallback
        yolo_path = settings.MODELS_DIR / "yolo_wagon.pt"
        if not yolo_path.exists():
            if setup_yolo_fallback():
                success_count += 1
        
        # Scene classifier fallback
        scene_path = settings.MODELS_DIR / "scene_classifier.pt"
        if not scene_path.exists():
            if create_mock_scene_classifier():
                success_count += 1
    
    # Summary
    logger.info("=" * 50)
    logger.info(f"Model setup complete: {success_count}/{total_models} models available")
    
    if success_count == total_models:
        logger.info("✅ All models are ready!")
    elif success_count > 0:
        logger.warning(f"⚠️  {total_models - success_count} models missing, but fallbacks available")
        logger.info("The system will work with reduced accuracy")
    else:
        logger.error("❌ No models available - the system may not work properly")
        return 1
    
    # List available models
    logger.info("\nAvailable models:")
    for model_file in settings.MODELS_DIR.glob("*.pt"):
        size_mb = model_file.stat().st_size / (1024 * 1024)
        logger.info(f"  - {model_file.name} ({size_mb:.1f} MB)")
    
    logger.info(f"\nTo start the system, run:")
    logger.info(f"  python app/main.py")
    logger.info(f"  # or")
    logger.info(f"  python main.py")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
