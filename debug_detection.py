#!/usr/bin/env python3
"""
Debug tool for wagon number detection.
Shows all processing steps and allows parameter tuning.
"""

import sys
import cv2
import numpy as np
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from detection import MorphologyDetector
from utils import VideoManager


class DebugDetector:
    """Debug version of morphology detector with parameter tuning."""
    
    def __init__(self):
        self.char_height = 47
        self.blur_size = 5
        self.morph_width = 17
        self.morph_height = 3
        self.min_area = 100
        self.max_area = 10000
        self.min_aspect_ratio = 1.5
        self.max_aspect_ratio = 8.0
        
    def get_blur_size(self):
        return max(1, self.blur_size)
    
    def process_frame_debug(self, frame):
        """Process frame and return all intermediate steps."""
        # Convert to grayscale
        if len(frame.shape) == 3:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        else:
            gray = frame.copy()
        
        # Apply blur
        blur_size = self.get_blur_size()
        if blur_size > 1:
            blurred = cv2.blur(gray, (blur_size, blur_size))
        else:
            blurred = gray.copy()
        
        # Sobel edge detection
        sobel = cv2.Sobel(blurred, cv2.CV_8U, 1, 0, ksize=3, scale=1, delta=0)
        
        # Threshold
        _, threshold = cv2.threshold(sobel, 0, 255, cv2.THRESH_OTSU + cv2.THRESH_BINARY)
        
        # Morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (self.morph_width, self.morph_height))
        morphed = cv2.morphologyEx(threshold, cv2.MORPH_CLOSE, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(morphed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter contours
        valid_rects = []
        result_frame = frame.copy()
        
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            area = w * h
            aspect_ratio = w / h if h > 0 else 0
            
            # Apply filters
            if (self.min_area <= area <= self.max_area and 
                self.min_aspect_ratio <= aspect_ratio <= self.max_aspect_ratio):
                valid_rects.append((x, y, w, h))
                # Draw valid rectangles in green
                cv2.rectangle(result_frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                # Add text with info
                cv2.putText(result_frame, f"A:{area:.0f} R:{aspect_ratio:.1f}", 
                           (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            else:
                # Draw rejected rectangles in red
                cv2.rectangle(result_frame, (x, y), (x + w, y + h), (0, 0, 255), 1)
        
        return {
            'original': frame,
            'gray': gray,
            'blurred': blurred,
            'sobel': sobel,
            'threshold': threshold,
            'morphed': morphed,
            'result': result_frame,
            'contours_count': len(contours),
            'valid_count': len(valid_rects),
            'valid_rects': valid_rects
        }


def create_debug_window():
    """Create debug window with parameter trackbars."""
    window_name = "Wagon Detection Debug"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, 1200, 800)
    
    # Create trackbars for parameter adjustment
    cv2.createTrackbar("Char Height", window_name, 47, 200, lambda x: None)
    cv2.createTrackbar("Blur Size", window_name, 5, 20, lambda x: None)
    cv2.createTrackbar("Morph Width", window_name, 17, 50, lambda x: None)
    cv2.createTrackbar("Morph Height", window_name, 3, 20, lambda x: None)
    cv2.createTrackbar("Min Area", window_name, 100, 2000, lambda x: None)
    cv2.createTrackbar("Max Area", window_name, 10000, 50000, lambda x: None)
    cv2.createTrackbar("Min Aspect*10", window_name, 15, 100, lambda x: None)  # *10 for precision
    cv2.createTrackbar("Max Aspect*10", window_name, 80, 200, lambda x: None)
    
    return window_name


def update_detector_params(detector, window_name):
    """Update detector parameters from trackbars."""
    detector.char_height = cv2.getTrackbarPos("Char Height", window_name)
    detector.blur_size = cv2.getTrackbarPos("Blur Size", window_name)
    detector.morph_width = cv2.getTrackbarPos("Morph Width", window_name)
    detector.morph_height = cv2.getTrackbarPos("Morph Height", window_name)
    detector.min_area = cv2.getTrackbarPos("Min Area", window_name)
    detector.max_area = cv2.getTrackbarPos("Max Area", window_name)
    detector.min_aspect_ratio = cv2.getTrackbarPos("Min Aspect*10", window_name) / 10.0
    detector.max_aspect_ratio = cv2.getTrackbarPos("Max Aspect*10", window_name) / 10.0


def create_combined_display(debug_results):
    """Create a combined display of all processing steps."""
    original = debug_results['original']
    gray = debug_results['gray']
    sobel = debug_results['sobel']
    threshold = debug_results['threshold']
    morphed = debug_results['morphed']
    result = debug_results['result']
    
    # Resize images to fit display
    height = 200
    width = int(height * original.shape[1] / original.shape[0])
    
    # Convert grayscale images to BGR for concatenation
    gray_bgr = cv2.cvtColor(cv2.resize(gray, (width, height)), cv2.COLOR_GRAY2BGR)
    sobel_bgr = cv2.cvtColor(cv2.resize(sobel, (width, height)), cv2.COLOR_GRAY2BGR)
    threshold_bgr = cv2.cvtColor(cv2.resize(threshold, (width, height)), cv2.COLOR_GRAY2BGR)
    morphed_bgr = cv2.cvtColor(cv2.resize(morphed, (width, height)), cv2.COLOR_GRAY2BGR)
    original_resized = cv2.resize(original, (width, height))
    result_resized = cv2.resize(result, (width, height))
    
    # Add labels
    def add_label(img, text):
        cv2.putText(img, text, (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(img, text, (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
        return img
    
    add_label(original_resized, "Original")
    add_label(gray_bgr, "Grayscale")
    add_label(sobel_bgr, "Sobel")
    add_label(threshold_bgr, "Threshold")
    add_label(morphed_bgr, "Morphology")
    add_label(result_resized, f"Result ({debug_results['valid_count']} found)")
    
    # Combine images
    top_row = np.hstack([original_resized, gray_bgr, sobel_bgr])
    bottom_row = np.hstack([threshold_bgr, morphed_bgr, result_resized])
    combined = np.vstack([top_row, bottom_row])
    
    return combined


def main():
    """Main debug application."""
    print("Wagon Detection Debug Tool")
    print("=" * 30)
    
    # Setup video manager
    video_manager = VideoManager()
    
    # Choose video file
    video_path = video_manager.choose_video_file()
    if not video_path or not video_manager.open_video_file(video_path):
        print("Failed to open video file")
        return
    
    print(f"Opened: {video_path.name}")
    print(f"Total frames: {video_manager.get_frame_count()}")
    print("\nControls:")
    print("- ESC: Exit")
    print("- SPACE: Pause/Resume")
    print("- S: Save current frame analysis")
    print("- R: Reset parameters to defaults")
    print("- Use trackbars to adjust detection parameters")
    
    # Create debug detector and window
    detector = DebugDetector()
    window_name = create_debug_window()
    
    paused = False
    frame_count = 0
    
    try:
        while True:
            if not paused:
                success, frame = video_manager.read_frame()
                if not success:
                    print("End of video reached")
                    break
                frame_count += 1
            
            # Update detector parameters from trackbars
            update_detector_params(detector, window_name)
            
            # Process frame
            debug_results = detector.process_frame_debug(frame)
            
            # Create combined display
            display = create_combined_display(debug_results)
            
            # Add statistics
            stats_text = [
                f"Frame: {frame_count}",
                f"Contours: {debug_results['contours_count']}",
                f"Valid: {debug_results['valid_count']}",
                f"Params: CH={detector.char_height} B={detector.blur_size}",
                f"Morph: {detector.morph_width}x{detector.morph_height}",
                f"Area: {detector.min_area}-{detector.max_area}",
                f"Aspect: {detector.min_aspect_ratio:.1f}-{detector.max_aspect_ratio:.1f}"
            ]
            
            y_offset = display.shape[0] - 150
            for i, text in enumerate(stats_text):
                cv2.putText(display, text, (10, y_offset + i * 20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                cv2.putText(display, text, (10, y_offset + i * 20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            
            cv2.imshow(window_name, display)
            
            # Handle keyboard input
            key = cv2.waitKey(30 if not paused else 0) & 0xFF
            
            if key == 27:  # ESC
                break
            elif key == 32:  # SPACE
                paused = not paused
                print("Paused" if paused else "Resumed")
            elif key == ord('s'):  # Save frame
                save_name = f"debug_frame_{frame_count:04d}.jpg"
                cv2.imwrite(save_name, display)
                print(f"Saved: {save_name}")
            elif key == ord('r'):  # Reset parameters
                cv2.setTrackbarPos("Char Height", window_name, 47)
                cv2.setTrackbarPos("Blur Size", window_name, 5)
                cv2.setTrackbarPos("Morph Width", window_name, 17)
                cv2.setTrackbarPos("Morph Height", window_name, 3)
                cv2.setTrackbarPos("Min Area", window_name, 100)
                cv2.setTrackbarPos("Max Area", window_name, 10000)
                cv2.setTrackbarPos("Min Aspect*10", window_name, 15)
                cv2.setTrackbarPos("Max Aspect*10", window_name, 80)
                print("Parameters reset to defaults")
    
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    
    finally:
        video_manager.release()
        cv2.destroyAllWindows()
        print("Debug session completed")


if __name__ == "__main__":
    main()
