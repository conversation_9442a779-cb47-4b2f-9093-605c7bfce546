"""
Scene Classifier for Railway Wagon Recognition

Implements ResNet-18 based scene classification to identify:
- wagon: Frame contains a railway wagon
- gap: Frame shows gap between wagons  
- background: Frame shows background/no relevant content

Based on the research paper achieving 36 FPS on CPU.
"""

import logging
from typing import Tuple, Optional
from pathlib import Path

import cv2
import numpy as np
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torchvision.models import resnet18

from app.core.config import settings


class SceneClassifier:
    """
    ResNet-18 based scene classifier for wagon detection.
    
    Classifies video frames into three categories:
    - wagon: Contains a railway wagon
    - gap: Shows gap between wagons
    - background: Background or irrelevant content
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.classes = ['background', 'gap', 'wagon']
        
        # Image preprocessing pipeline
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        self.logger.info(f"Scene classifier initialized on device: {self.device}")
    
    def load_model(self, model_path: Optional[Path] = None) -> bool:
        """
        Load the pre-trained scene classifier model.
        
        Args:
            model_path: Optional path to model file
            
        Returns:
            True if model loaded successfully, False otherwise
        """
        if model_path is None:
            model_path = settings.MODELS_DIR / settings.SCENE_CLASSIFIER_MODEL
        
        try:
            # Create ResNet-18 model with 3 output classes
            self.model = resnet18(pretrained=False)
            self.model.fc = nn.Linear(self.model.fc.in_features, len(self.classes))
            
            if model_path.exists():
                # Load pre-trained weights
                self.logger.info(f"Loading scene classifier from: {model_path}")
                checkpoint = torch.load(model_path, map_location=self.device)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.logger.info("Scene classifier loaded successfully")
            else:
                # Use randomly initialized model (for development)
                self.logger.warning(f"Model file not found: {model_path}")
                self.logger.warning("Using randomly initialized model - accuracy will be poor!")
                self.logger.info("To download pre-trained models, run: python scripts/download_models.py")
            
            self.model.to(self.device)
            self.model.eval()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load scene classifier: {e}")
            return False
    
    def classify(self, frame: np.ndarray) -> Tuple[str, float]:
        """
        Classify a video frame.
        
        Args:
            frame: Input video frame (BGR format)
            
        Returns:
            Tuple of (class_name, confidence_score)
        """
        if self.model is None:
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        try:
            # Preprocess frame
            if len(frame.shape) == 3 and frame.shape[2] == 3:
                # Convert BGR to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            else:
                frame_rgb = frame
            
            # Apply transforms
            input_tensor = self.transform(frame_rgb).unsqueeze(0).to(self.device)
            
            # Run inference
            with torch.no_grad():
                outputs = self.model(input_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                confidence, predicted = torch.max(probabilities, 1)
                
                class_name = self.classes[predicted.item()]
                confidence_score = confidence.item()
            
            return class_name, confidence_score
            
        except Exception as e:
            self.logger.error(f"Error during scene classification: {e}")
            return 'background', 0.0
    
    def classify_batch(self, frames: list) -> list:
        """
        Classify multiple frames in a batch for better performance.
        
        Args:
            frames: List of video frames
            
        Returns:
            List of (class_name, confidence_score) tuples
        """
        if self.model is None:
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        if not frames:
            return []
        
        try:
            # Preprocess all frames
            batch_tensors = []
            for frame in frames:
                if len(frame.shape) == 3 and frame.shape[2] == 3:
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                else:
                    frame_rgb = frame
                
                tensor = self.transform(frame_rgb)
                batch_tensors.append(tensor)
            
            # Stack into batch
            batch_tensor = torch.stack(batch_tensors).to(self.device)
            
            # Run batch inference
            with torch.no_grad():
                outputs = self.model(batch_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                confidences, predictions = torch.max(probabilities, 1)
                
                results = []
                for pred, conf in zip(predictions, confidences):
                    class_name = self.classes[pred.item()]
                    confidence_score = conf.item()
                    results.append((class_name, confidence_score))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error during batch scene classification: {e}")
            return [('background', 0.0)] * len(frames)
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model."""
        if self.model is None:
            return {'status': 'not_loaded'}
        
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        return {
            'status': 'loaded',
            'architecture': 'ResNet-18',
            'classes': self.classes,
            'device': str(self.device),
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'input_size': (224, 224),
            'expected_fps': 36  # Based on research paper
        }


class MockSceneClassifier(SceneClassifier):
    """
    Mock scene classifier for development/testing.
    
    Provides deterministic classification based on simple heuristics
    when pre-trained models are not available.
    """
    
    def __init__(self):
        super().__init__()
        self.logger.info("Using mock scene classifier for development")
    
    def load_model(self, model_path: Optional[Path] = None) -> bool:
        """Mock model loading - always succeeds."""
        self.logger.info("Mock scene classifier loaded")
        return True
    
    def classify(self, frame: np.ndarray) -> Tuple[str, float]:
        """
        Mock classification based on simple heuristics.
        
        This is a placeholder implementation that uses basic image
        properties to simulate scene classification.
        """
        try:
            # Convert to grayscale for analysis
            if len(frame.shape) == 3:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = frame
            
            # Simple heuristics based on image properties
            mean_brightness = np.mean(gray)
            std_brightness = np.std(gray)
            
            # Edge density
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            # Mock classification logic
            if edge_density > 0.1 and std_brightness > 30:
                # High edge density and variation suggests wagon
                return 'wagon', 0.85
            elif edge_density < 0.05 and mean_brightness < 50:
                # Low edge density and dark suggests gap
                return 'gap', 0.80
            else:
                # Default to background
                return 'background', 0.75
                
        except Exception as e:
            self.logger.error(f"Error in mock classification: {e}")
            return 'background', 0.0
