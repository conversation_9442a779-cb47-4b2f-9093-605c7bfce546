# Railway Wagon Recognition System - Architecture

## System Overview

This system implements a comprehensive AI-powered railway wagon counting and number recognition solution based on the research paper "Real-time Train Wagon Counting and Number Recognition Algorithm" and production requirements.

## Architecture Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Video Input   │───▶│  AI Pipeline    │───▶│   Web API       │
│   - File upload │    │  - Scene detect │    │   - REST API    │
│   - RTSP stream │    │  - Object detect│    │   - WebUI       │
│   - Camera feed │    │  - OCR process  │    │   - Webhooks    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ↓
                       ┌─────────────────┐    ┌─────────────────┐
                       │    Database     │    │   Monitoring    │
                       │   - Results     │    │   - Logs        │
                       │   - Statistics  │    │   - Metrics     │
                       │   - Settings    │    │   - Alerts      │
                       └─────────────────┘    └─────────────────┘
```

## AI Pipeline Components

### 1. Scene Classifier (ResNet-18)
- **Purpose**: Classify frames as wagon/gap/background
- **Input**: Video frames (720p-4K)
- **Output**: Frame classification + confidence
- **Performance**: 36 FPS on CPU
- **Accuracy**: 98%+ wagon counting

### 2. Object Detector (YOLO v8)
- **Purpose**: Detect wagons and locate number regions
- **Input**: Frames classified as "wagon"
- **Output**: Bounding boxes for wagons and numbers
- **Performance**: 10+ FPS on CPU
- **Accuracy**: 95%+ detection rate

### 3. OCR Engine (EasyOCR)
- **Purpose**: Recognize digits in detected number regions
- **Input**: Cropped number images
- **Output**: Recognized text + confidence scores
- **Features**: Multi-language support, robust to distortions
- **Accuracy**: 97%+ single frame, 99%+ multi-frame

### 4. Number Validator
- **Purpose**: Validate wagon numbers using checksums
- **Input**: Recognized number strings
- **Output**: Validated numbers + confidence
- **Features**: Railway-specific validation rules

### 5. Object Tracker
- **Purpose**: Track wagons across frames for counting
- **Input**: Detection results from multiple frames
- **Output**: Unique wagon count + trajectories
- **Features**: Multi-object tracking, ID assignment

## Directory Structure

```
wagons_recognition_python/
├── app/                        # FastAPI application
│   ├── __init__.py
│   ├── main.py                # FastAPI app entry point
│   ├── api/                   # API endpoints
│   │   ├── __init__.py
│   │   ├── jobs.py           # Job management
│   │   ├── upload.py         # File upload
│   │   └── results.py        # Results retrieval
│   ├── core/                  # Core business logic
│   │   ├── __init__.py
│   │   ├── config.py         # Configuration
│   │   ├── security.py       # Authentication
│   │   └── database.py       # Database setup
│   ├── models/                # Database models
│   │   ├── __init__.py
│   │   ├── job.py            # Job model
│   │   ├── result.py         # Result model
│   │   └── user.py           # User model
│   └── templates/             # HTML templates
│       ├── index.html        # Main interface
│       ├── results.html      # Results display
│       └── upload.html       # File upload
├── ai/                        # AI pipeline components
│   ├── __init__.py
│   ├── pipeline.py           # Main AI pipeline
│   ├── scene_classifier.py   # Scene classification
│   ├── object_detector.py    # YOLO v8 integration
│   ├── ocr_engine.py         # OCR processing
│   ├── validator.py          # Number validation
│   └── tracker.py            # Object tracking
├── utils/                     # Utility functions
│   ├── __init__.py
│   ├── video_processor.py    # Video processing
│   ├── image_utils.py        # Image utilities
│   └── metrics.py            # Performance metrics
├── models/                    # Pre-trained models
│   ├── scene_classifier.pt   # Scene classifier weights
│   ├── yolo_wagon.pt         # YOLO wagon detector
│   └── README.md             # Model information
├── static/                    # Static web assets
│   ├── css/
│   ├── js/
│   └── images/
├── tests/                     # Test suite
│   ├── __init__.py
│   ├── test_api.py           # API tests
│   ├── test_ai.py            # AI pipeline tests
│   └── test_utils.py         # Utility tests
├── docker/                    # Docker configurations
│   ├── Dockerfile.prod       # Production Dockerfile
│   ├── docker-compose.prod.yml
│   └── nginx.conf            # Nginx configuration
├── scripts/                   # Utility scripts
│   ├── download_models.py    # Download pre-trained models
│   ├── train_classifier.py   # Training scripts
│   └── benchmark.py          # Performance benchmarking
├── config/                    # Configuration files
│   ├── development.env       # Development settings
│   ├── production.env        # Production settings
│   └── logging.yaml          # Logging configuration
├── requirements.txt           # Python dependencies
├── requirements-dev.txt       # Development dependencies
├── docker-compose.yml         # Development Docker setup
├── README.md                  # Project documentation
└── ARCHITECTURE.md           # This file
```

## Data Flow

1. **Input Processing**
   - Video files uploaded via web interface
   - RTSP streams connected for real-time processing
   - Frames extracted and queued for processing

2. **AI Pipeline Processing**
   - Scene classifier filters relevant frames
   - Object detector locates wagons and numbers
   - OCR engine recognizes number text
   - Validator checks number correctness
   - Tracker maintains wagon counts

3. **Result Storage**
   - Results stored in database with timestamps
   - Statistics and metrics collected
   - Reports generated for export

4. **API Response**
   - Real-time updates via WebSocket
   - REST API for result retrieval
   - Webhook notifications for completed jobs

## Performance Requirements

- **Processing Speed**: 10+ FPS on CPU (Intel i5 equivalent)
- **Accuracy**: 98%+ wagon counting, 95%+ number recognition
- **Latency**: <2 seconds for real-time processing
- **Scalability**: Support for 4+ concurrent video streams
- **Uptime**: 99.5% availability target

## Technology Stack

- **Backend**: Python 3.9+, FastAPI, SQLAlchemy
- **AI/ML**: PyTorch, YOLO v8, EasyOCR, OpenCV
- **Database**: SQLite (dev), PostgreSQL (prod)
- **Frontend**: HTML5, Bootstrap 5, JavaScript
- **Deployment**: Docker, Docker Compose, Kubernetes
- **Monitoring**: Prometheus, Grafana, structured logging

## Security Considerations

- JWT-based authentication for API access
- HTTPS for all web interfaces
- Input validation and sanitization
- Rate limiting for API endpoints
- Secure file upload handling
- Environment-based configuration

## Deployment Options

### Development
- Docker Compose with hot reload
- SQLite database
- Local file storage
- Debug logging enabled

### Production
- Kubernetes deployment
- PostgreSQL database
- Object storage (S3/MinIO)
- Structured logging
- Monitoring and alerting
- Load balancing
- Auto-scaling

## Migration Path

1. **Phase 1**: Replace morphological detection with YOLO v8
2. **Phase 2**: Add scene classification and OCR
3. **Phase 3**: Implement web interface and API
4. **Phase 4**: Add real-time streaming support
5. **Phase 5**: Production deployment and monitoring
