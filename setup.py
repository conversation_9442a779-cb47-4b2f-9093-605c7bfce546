"""
Setup script for Wagons Recognition Python implementation.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    requirements = requirements_path.read_text(encoding="utf-8").strip().split("\n")

setup(
    name="wagons-recognition-python",
    version="1.0.0",
    description="Railway wagon number detection using computer vision",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Python Port",
    author_email="",
    url="https://github.com/igororlov92/WagonsRecognition",
    packages=find_packages(),
    install_requires=requirements,
    python_requires=">=3.8",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Image Recognition",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    keywords="computer-vision opencv railway wagon detection morphology",
    entry_points={
        "console_scripts": [
            "wagons-recognition=main:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
