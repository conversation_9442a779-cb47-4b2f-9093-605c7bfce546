"""
Base detector class for wagon number detection.
"""

from abc import ABC, abstractmethod
from typing import List, Tuple
import cv2
import numpy as np

from config import COLOR_RED, MIN_CONTOUR_AREA, MAX_CONTOUR_AREA, MIN_ASPECT_RATIO, MAX_ASPECT_RATIO


class Detector(ABC):
    """
    Abstract base class for wagon number detectors.
    """
    
    def __init__(self):
        self._rects: List[Tuple[int, int, int, int]] = []
    
    @abstractmethod
    def detect(self, input_image: np.ndarray) -> None:
        """
        Detect wagon numbers in the input image.
        
        Args:
            input_image: Input image as numpy array
        """
        pass
    
    def get_rects(self) -> List[Tuple[int, int, int, int]]:
        """
        Get detected rectangles.
        
        Returns:
            List of rectangles as (x, y, width, height) tuples
        """
        return self._rects.copy()
    
    def draw_rects(self, image: np.ndarray) -> None:
        """
        Draw detected rectangles on the image.
        
        Args:
            image: Image to draw rectangles on (modified in-place)
        """
        for rect in self._rects:
            x, y, w, h = rect
            cv2.rectangle(
                image,
                (x, y),
                (x + w, y + h),
                COLOR_RED,
                thickness=2
            )
    
    def verify_size(self, rect: Tuple[int, int, int, int]) -> bool:
        """
        Verify if the rectangle size is valid for a wagon number.
        
        Args:
            rect: Rectangle as (x, y, width, height) tuple
            
        Returns:
            True if rectangle size is valid, False otherwise
        """
        x, y, w, h = rect
        
        # Basic check: width should be greater than height for wagon numbers
        if h > w:
            return False
            
        # Additional size constraints can be added here
        # For example: minimum/maximum area, aspect ratio, etc.
        
        return True
    
    def clear_rects(self) -> None:
        """Clear all detected rectangles."""
        self._rects.clear()
